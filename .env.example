# Mobile Booking Driver App Environment Configuration

# API Configuration
EXPO_PUBLIC_API_BASE_URL=https://api.mobilebooking.com/v1
EXPO_PUBLIC_WS_BASE_URL=wss://ws.mobilebooking.com/v1

# Third-party API Keys
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Environment
NODE_ENV=development

# Feature Flags
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_REAL_TIME_UPDATES=true
EXPO_PUBLIC_ENABLE_VOICE_NAVIGATION=true
EXPO_PUBLIC_ENABLE_BIOMETRIC_AUTH=false

# Development Configuration
EXPO_PUBLIC_ENABLE_DEBUG_LOGS=true
EXPO_PUBLIC_API_TIMEOUT=30000

# Production Configuration (set these in production)
# EXPO_PUBLIC_API_BASE_URL=https://api.mobilebooking.com/v1
# EXPO_PUBLIC_WS_BASE_URL=wss://ws.mobilebooking.com/v1
# EXPO_PUBLIC_ENABLE_DEBUG_LOGS=false