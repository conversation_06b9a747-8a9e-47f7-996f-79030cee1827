import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GlobalStateProvider } from './src/context/ApiIntegratedGlobalStateContext';
import { ThemeProvider } from './src/context/ThemeContext';
import AppNavigator from './src/navigation/AppNavigator';

export default function App() {
  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <GlobalStateProvider>
          <AppNavigator />
          <StatusBar style="auto" />
        </GlobalStateProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}
