{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "babel-plugin-module-resolver": "^5.0.2", "expo": "53.0.20", "expo-dev-client": "~5.2.4", "expo-location": "^18.1.6", "expo-modules-core": "~2.5.0", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-hook-form": "^7.60.0", "react-native": "0.79.5", "react-native-maps": "1.20.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}