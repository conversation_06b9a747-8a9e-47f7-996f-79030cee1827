import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Header from './Header';
import { useTheme } from '../context/ThemeContext';

interface AppLayoutProps {
    children: React.ReactNode;
    title?: string;
    subtitle?: string;
    rightComponent?: React.ReactNode;
    headerBackgroundColor?: string;
    hideHeader?: boolean;
    onMenuPress?: () => void;
    showThemeToggle?: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = ({
    children,
    title,
    subtitle,
    rightComponent,
    headerBackgroundColor,
    hideHeader = false,
    onMenuPress,
    showThemeToggle = false,
}) => {
    const { colors } = useTheme();
    
    return (
        <SafeAreaProvider>
            <View style={[styles.container, { backgroundColor: colors.background }]}>
                {!hideHeader && title && onMenuPress && (
                    <Header
                        title={title}
                        subtitle={subtitle}
                        onMenuPress={onMenuPress}
                        rightComponent={rightComponent}
                        backgroundColor={headerBackgroundColor}
                        showThemeToggle={showThemeToggle}
                    />
                )}

                <View style={[styles.content, { backgroundColor: colors.background }]}>
                    {children}
                </View>
            </View>
        </SafeAreaProvider>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    content: {
        flex: 1,
    },
});

export default AppLayout;