import React from 'react';
import { View, StyleSheet } from 'react-native';

interface BottomSpacerProps {
    isMapScreen?: boolean;
    isAuthScreen?: boolean;
}

const BottomSpacer: React.FC<BottomSpacerProps> = ({
    isMapScreen = false,
    isAuthScreen = false
}) => {
    const shouldShowBottomContainer = !isMapScreen && !isAuthScreen;

    if (!shouldShowBottomContainer) {
        return null;
    }

    return <View style={styles.bottomContainer} />;
};

const styles = StyleSheet.create({
    bottomContainer: {
        height: 100,
        width: '100%',
        backgroundColor: 'transparent',
    },
});

export default BottomSpacer;