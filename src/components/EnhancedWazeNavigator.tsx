import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, StatusBar, Alert } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as Speech from 'expo-speech';
import { NavigationService } from '../services/NavigationService';
import SpeedSimulator from './SpeedSimulator';

// Extend LocationSubscription to include our custom properties
declare module 'expo-location' {
  interface LocationSubscription {
    lastUpdateTime?: number;
    etaUpdateInterval?: NodeJS.Timeout;
  }
}

interface NavigationStep {
  instruction: string;
  distance: string;
  duration: string;
  maneuver: string;
  location: {
    latitude: number;
    longitude: number;
  };
}

interface EnhancedWazeNavigatorProps {
  destination: { latitude: number; longitude: number };
  destinationName?: string;
  onClose?: () => void;
  onArrival?: () => void;
}

// Get screen dimensions
const dimensions = Dimensions.get('window');

export const EnhancedWazeNavigator: React.FC<EnhancedWazeNavigatorProps> = ({
  destination,
  destinationName = "Destination",
  onClose,
  onArrival
}) => {
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [currentStep, setCurrentStep] = useState<NavigationStep | null>(null);
  const [nextStep, setNextStep] = useState<NavigationStep | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isNavigating, setIsNavigating] = useState(false);
  const [routeCoordinates, setRouteCoordinates] = useState<Array<{ latitude: number, longitude: number }>>([]);
  const [mapRegion, setMapRegion] = useState({
    latitude: 14.5995, // Default center
    longitude: 120.9842,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [remainingDistance, setRemainingDistance] = useState('');
  const [remainingTime, setRemainingTime] = useState('');
  const [arrivalTime, setArrivalTime] = useState('');
  const [isMuted, setIsMuted] = useState(false);
  const [mapOrientation, setMapOrientation] = useState('north-up'); // 'north-up' or 'heading-up'

  const locationSubscription = useRef<Location.LocationSubscription | null>(null);
  const mapRef = useRef<MapView>(null);
  const navigationService = NavigationService.getInstance();

  // Request location permissions
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required for navigation');
        return false;
      }
      setHasLocationPermission(true);
      return true;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  // Get current location
  const getCurrentLocation = async () => {
    try {
      if (!hasLocationPermission) {
        const granted = await requestLocationPermission();
        if (!granted) return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High
      });

      const coords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      };

      setCurrentLocation(coords);
      return coords;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  };

  // Start navigation
  const startNavigation = async () => {
    const location = await getCurrentLocation();
    if (!location) {
      Alert.alert('Error', 'Could not get your current location');
      return;
    }

    setIsNavigating(true);

    try {
      // Get route from NavigationService
      const route = await navigationService.getDetailedDirections(location, destination);

      if (!route) {
        Alert.alert('Error', 'Could not get directions to destination');
        setIsNavigating(false);
        return;
      }

      // Set route coordinates
      const coordinates = decodePolyline(route.polyline);
      setRouteCoordinates(coordinates);

      // Set initial step
      if (route.steps.length > 0) {
        setCurrentStep(route.steps[0]);
        setCurrentStepIndex(0);

        // Set next step if available
        if (route.steps.length > 1) {
          setNextStep(route.steps[1]);
        }
      }

      // Set remaining distance and time
      setRemainingDistance(route.totalDistance);
      setRemainingTime(route.totalDuration);

      // Calculate arrival time
      const now = new Date();
      const durationMatch = route.totalDuration.match(/(\d+)\s+min/);
      if (durationMatch) {
        const minutes = parseInt(durationMatch[1], 10);
        now.setMinutes(now.getMinutes() + minutes);
        setArrivalTime(now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
      }

      // Start location tracking
      startLocationTracking();

      // Announce first instruction
      if (!isMuted && route.steps.length > 0) {
        announceInstruction(route.steps[0].instruction);
      }

      // Center map on current location with heading up
      updateMapRegion(location, 0.01);

    } catch (error) {
      console.error('Error starting navigation:', error);
      Alert.alert('Error', 'Could not start navigation');
      setIsNavigating(false);
    }
  };

  // Start tracking location updates
  const startLocationTracking = async () => {
    try {
      // Stop any existing subscription
      if (locationSubscription.current) {
        locationSubscription.current.remove();
      }

      // Start new subscription with higher accuracy settings
      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 1000, // Update every second
          distanceInterval: 3, // Update every 3 meters (more frequent updates)
        },
        (location) => {
          const { latitude, longitude, heading } = location.coords;

          // Update current location
          const currentPos = { latitude, longitude };
          setCurrentLocation(currentPos);

          // Update map orientation if in heading-up mode
          if (mapOrientation === 'heading-up' && heading !== null) {
            mapRef.current?.animateCamera({
              center: currentPos,
              heading: heading,
              pitch: 60,
              zoom: 18,
            });
          }

          // Check if we've reached the current step
          handleLocationUpdate(currentPos, heading);

          // Update remaining info more frequently based on distance traveled
          // This ensures we have accurate ETA and speed data
          const lastUpdateTime = locationSubscription.current?.lastUpdateTime || 0;
          const currentTime = Date.now();

          // Update every 15 seconds or every 50 meters, whichever comes first
          if (currentTime - lastUpdateTime > 15000) {
            updateRemainingInfo(currentPos);
            locationSubscription.current.lastUpdateTime = currentTime;
          }
        }
      );

      // Add a custom property to track last update time
      locationSubscription.current.lastUpdateTime = Date.now();

      // Set up a timer to periodically update ETA from API regardless of movement
      // This ensures we get traffic updates even when stationary
      const etaUpdateInterval = setInterval(() => {
        if (currentLocation) {
          updateRemainingInfo(currentLocation);
        }
      }, 30000); // Every 30 seconds

      // Store the interval for cleanup
      locationSubscription.current.etaUpdateInterval = etaUpdateInterval;

    } catch (error) {
      console.error('Error tracking location:', error);
    }
  };

  // Handle location updates during navigation
  const handleLocationUpdate = (
    position: { latitude: number; longitude: number },
    currentHeading: number | null
  ) => {
    if (!currentStep || !isNavigating) return;

    // Calculate distance to current step endpoint
    const distanceToStep = calculateDistance(
      position,
      currentStep.location
    );

    // If within 30 meters of current step, move to next step
    if (distanceToStep < 30) {
      moveToNextStep();
    }

    // Update remaining distance and time based on current position
    updateRemainingInfo(position);

    // Update map orientation if in heading-up mode and heading is available
    if (mapOrientation === 'heading-up' && currentHeading !== null && mapRef.current) {
      mapRef.current.animateCamera({
        center: position,
        heading: currentHeading,
        pitch: 60,
        zoom: 18,
      });
    }
  };

  // Move to next navigation step
  const moveToNextStep = () => {
    if (!navigationService.getCurrentState().currentRoute) return;

    const route = navigationService.getCurrentState().currentRoute;
    const newIndex = currentStepIndex + 1;

    if (newIndex >= route.steps.length) {
      // Navigation complete
      handleArrival();
    } else {
      // Move to next step
      setCurrentStepIndex(newIndex);
      setCurrentStep(route.steps[newIndex]);

      // Set next step if available
      if (newIndex + 1 < route.steps.length) {
        setNextStep(route.steps[newIndex + 1]);
      } else {
        setNextStep(null);
      }

      // Announce next instruction
      if (!isMuted) {
        announceInstruction(route.steps[newIndex].instruction);
      }
    }
  };

  // Handle arrival at destination
  const handleArrival = () => {
    setIsNavigating(false);

    // Stop location tracking
    if (locationSubscription.current) {
      locationSubscription.current.remove();
      locationSubscription.current = null;
    }

    // Announce arrival
    if (!isMuted) {
      Speech.speak('You have arrived at your destination');
    }

    Alert.alert('Arrived', 'You have arrived at your destination');
    onArrival?.();
  };

  // Update remaining distance and time based on current position
  const updateRemainingInfo = async (position: { latitude: number; longitude: number }) => {
    try {
      // Get updated ETA with traffic from Google Maps API
      const eta = await navigationService.getTrafficAwareETA(position, destination);

      if (eta) {
        // Update remaining time with traffic-aware duration
        setRemainingTime(eta.duration);

        // Update remaining distance
        if (eta.distance) {
          setRemainingDistance(eta.distance);
        }

        // Set precise arrival time from Google's calculation
        if (eta.arrivalTime) {
          // Format time with leading zeros for consistency
          const hours = eta.arrivalTime.getHours();
          const minutes = eta.arrivalTime.getMinutes();
          const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          setArrivalTime(formattedTime);
        }

        // If we have a route with steps, update the current step's duration if needed
        if (navigationService.getCurrentState().currentRoute && currentStep) {
          const currentStepIndex = navigationService.getCurrentState().currentStepIndex;
          const remainingSteps = navigationService.getCurrentState().currentRoute.steps.slice(currentStepIndex);

          // If there's significant traffic delay, distribute it proportionally to remaining steps
          if (eta.trafficDelay > 60) { // More than 1 minute delay
            const totalBaseTime = remainingSteps.reduce((acc, step) => {
              // Extract numeric duration from step.duration (e.g., "5 mins" -> 5)
              const durationMatch = step.duration.match(/(\d+)/);
              return acc + (durationMatch ? parseInt(durationMatch[1], 10) : 0);
            }, 0);

            // Only update if we have valid base times
            if (totalBaseTime > 0) {
              // Update current step with traffic-aware duration
              const trafficDelayMinutes = Math.round(eta.trafficDelay / 60);
              const currentStepBaseTime = parseInt(currentStep.duration.match(/(\d+)/)?.[1] || "0", 10);
              const adjustedStepTime = currentStepBaseTime + Math.round((currentStepBaseTime / totalBaseTime) * trafficDelayMinutes);

              // Update the current step's duration
              setCurrentStep({
                ...currentStep,
                duration: `${adjustedStepTime} min`
              });
            }
          }
        }
      }
    } catch (error) {
      console.error('Error updating remaining info:', error);
    }
  };

  // Stop navigation
  const stopNavigation = () => {
    setIsNavigating(false);

    // Stop location tracking
    if (locationSubscription.current) {
      locationSubscription.current.remove();
      locationSubscription.current = null;
    }

    // Reset navigation state
    setCurrentStep(null);
    setNextStep(null);
    setCurrentStepIndex(0);
    setRouteCoordinates([]);

    // Close navigator
    onClose?.();
  };

  // Toggle mute/unmute
  const toggleMute = () => {
    setIsMuted(!isMuted);

    // Stop any ongoing speech if muting
    if (!isMuted) {
      Speech.stop();
    }
  };

  // Toggle map orientation
  const toggleMapOrientation = () => {
    const newOrientation = mapOrientation === 'north-up' ? 'heading-up' : 'north-up';
    setMapOrientation(newOrientation);

    if (newOrientation === 'north-up' && currentLocation) {
      mapRef.current?.animateCamera({
        center: currentLocation,
        heading: 0,
        pitch: 0,
        zoom: 16,
      });
    }
  };

  // Update map region
  const updateMapRegion = (center: { latitude: number; longitude: number }, delta: number = 0.01) => {
    setMapRegion({
      latitude: center.latitude,
      longitude: center.longitude,
      latitudeDelta: delta,
      longitudeDelta: delta,
    });
  };

  // Announce instruction using text-to-speech
  const announceInstruction = (instruction: string) => {
    if (isMuted) return;

    try {
      Speech.speak(instruction, {
        language: 'en-US',
        pitch: 1.0,
        rate: 0.9,
      });
    } catch (error) {
      console.log('Speech not available:', error);
    }
  };

  // Calculate distance between two points
  const calculateDistance = (
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (point1.latitude * Math.PI) / 180;
    const φ2 = (point2.latitude * Math.PI) / 180;
    const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Get maneuver icon
  const getManeuverIcon = (maneuver: string) => {
    switch (maneuver) {
      case 'turn-left':
      case 'turn-slight-left':
      case 'turn-sharp-left':
        return 'turn-left';
      case 'turn-right':
      case 'turn-slight-right':
      case 'turn-sharp-right':
        return 'turn-right';
      case 'straight':
      case 'continue':
        return 'straight';
      case 'merge':
        return 'merge-type';
      case 'roundabout-left':
      case 'roundabout-right':
        return 'roundabout-left';
      case 'exit':
        return 'exit-to-app';
      case 'ramp-left':
      case 'ramp-right':
        return 'call-merge';
      case 'fork-left':
      case 'fork-right':
        return 'call-split';
      default:
        return 'navigation';
    }
  };

  // Polyline decoder
  const decodePolyline = (encoded: string) => {
    const coordinates: Array<{ latitude: number, longitude: number }> = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b: number;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      coordinates.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return coordinates;
  };

  // Initialize component
  useEffect(() => {
    const initialize = async () => {
      const hasPermission = await requestLocationPermission();
      if (hasPermission) {
        const location = await getCurrentLocation();
        if (location) {
          updateMapRegion(location);
        }
      }
    };

    initialize();

    // Cleanup on unmount
    return () => {
      if (locationSubscription.current) {
        // Clear the ETA update interval if it exists
        if (locationSubscription.current.etaUpdateInterval) {
          clearInterval(locationSubscription.current.etaUpdateInterval);
        }
        locationSubscription.current.remove();
      }
      Speech.stop();
    };
  }, []);

  // Start navigation automatically when component mounts
  useEffect(() => {
    if (hasLocationPermission && currentLocation && !isNavigating) {
      startNavigation();
    }
  }, [hasLocationPermission, currentLocation]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Current Road Banner */}
      {isNavigating && currentStep && (
        <View style={styles.roadBanner}>
          <MaterialIcons name={getManeuverIcon(currentStep.maneuver)} size={24} color="#fff" />
          <Text style={styles.roadBannerText}>
            {currentStep.instruction}
          </Text>
        </View>
      )}

      {/* Map Container */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={mapRegion}
          showsUserLocation={true}
          showsMyLocationButton={false}
          showsTraffic={true}
          followsUserLocation={isNavigating}
          mapType="standard"
        >
          {/* Destination marker */}
          <Marker
            coordinate={destination}
            title={destinationName}
            pinColor="red"
          />

          {/* Route polyline */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="#5E72E4"
              strokeWidth={8}
              lineCap="round"
              lineJoin="round"
            />
          )}
        </MapView>

        {/* Navigation Controls */}

        {/* Real-time speed indicator */}
        {isNavigating && (
          <SpeedSimulator
            isActive={isNavigating}
            style={styles.speedIndicator}
            destination={destination}
          />
        )}

        {/* Current step info (bottom center) */}
        {isNavigating && currentStep && (
          <View style={styles.currentStepInfo}>
            <View style={styles.stepDot} />
            <Text style={styles.stepText}>
              {currentStep.distance} • {currentStep.duration || ''}
            </Text>
          </View>
        )}

        {/* Control buttons (right side) */}
        <View style={styles.controlButtons}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={toggleMapOrientation}
          >
            <MaterialIcons
              name={mapOrientation === 'north-up' ? 'navigation' : 'explore'}
              size={20}
              color="#666"
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={toggleMute}
          >
            <MaterialIcons
              name={isMuted ? 'volume-off' : 'volume-up'}
              size={20}
              color="#666"
            />
          </TouchableOpacity>
        </View>

        {/* Bottom info bar */}
        <View style={styles.bottomInfoBar}>
          <TouchableOpacity style={styles.closeButton} onPress={stopNavigation}>
            <MaterialIcons name="close" size={20} color="#666" />
          </TouchableOpacity>

          <View style={styles.tripInfo}>
            <Text style={styles.tripDuration}>{remainingTime}</Text>
            <Text style={styles.tripDetails}>{remainingDistance} • {arrivalTime}</Text>
          </View>

          <TouchableOpacity style={styles.routeOptionsButton}>
            <MaterialIcons name="alt-route" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  roadBanner: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    backgroundColor: '#4A7C59',
    borderRadius: 20,
    paddingVertical: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  roadBannerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 10,
    lineHeight: 24,
    flexShrink: 1,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  speedIndicator: {
    position: 'absolute',
    bottom: 140,
    left: 20,
    zIndex: 1000,
  },
  currentStepInfo: {
    position: 'absolute',
    bottom: 140,
    left: '50%',
    marginLeft: -80,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2196F3',
    marginRight: 8,
  },
  stepText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  controlButtons: {
    position: 'absolute',
    bottom: 140,
    right: 20,
    alignItems: 'center',
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  bottomInfoBar: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
    backgroundColor: '#fff',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tripInfo: {
    flex: 1,
    alignItems: 'center',
  },
  tripDuration: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 2,
  },
  tripDetails: {
    fontSize: 12,
    color: '#666',
  },
  routeOptionsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default EnhancedWazeNavigator;