import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Ride } from '../types';

interface ExpandableCustomerDetailsProps {
  ride: Ride;
  isExpanded: boolean;
  onToggle: () => void;
  showActions?: boolean;
}

const ExpandableCustomerDetails: React.FC<ExpandableCustomerDetailsProps> = ({
  ride,
  isExpanded,
  onToggle,
  showActions = false
}) => {
  const handleCallPassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Call Passenger',
      `Call ${passengerName} at ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Call', 
          onPress: () => {
            const phoneUrl = `tel:${phoneNumber}`;
            Linking.canOpenURL(phoneUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(phoneUrl);
                } else {
                  Alert.alert('Error', 'Phone calls are not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening phone app:', err);
                Alert.alert('Error', 'Failed to open phone app');
              });
          }
        }
      ]
    );
  };

  const handleMessagePassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Message Passenger',
      `Send SMS to ${passengerName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Send SMS', 
          onPress: () => {
            const smsUrl = `sms:${phoneNumber}`;
            Linking.canOpenURL(smsUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(smsUrl);
                } else {
                  Alert.alert('Error', 'SMS is not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening SMS app:', err);
                Alert.alert('Error', 'Failed to open SMS app');
              });
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.header} 
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <View style={styles.headerLeft}>
          <MaterialIcons name="person" size={20} color="#007AFF" />
          <Text style={styles.headerTitle}>Customer Details</Text>
        </View>
        <MaterialIcons 
          name={isExpanded ? "expand-less" : "expand-more"} 
          size={24} 
          color="#6D6D70" 
        />
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.expandedContent}>
          <View style={styles.customerInfo}>
            <View style={styles.customerRow}>
              <View style={styles.customerLeft}>
                <Text style={styles.customerName}>{ride.passengerName}</Text>
                <Text style={styles.customerPhone}>{ride.passengerPhone}</Text>
                <Text style={styles.customerCount}>
                  {ride.passengerCount > 1 ? `${ride.passengerCount} passengers` : '1 passenger'}
                </Text>
              </View>
              <View style={styles.customerRight}>
                <Text style={styles.priorityText}>
                  {ride.priority?.toUpperCase() || 'STANDARD'} PRIORITY
                </Text>
              </View>
            </View>

            <View style={styles.tripDetails}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Vehicle Type:</Text>
                <Text style={styles.detailValue}>{ride.vehicleType?.replace('_', ' ') || 'Standard'}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Payment:</Text>
                <Text style={styles.detailValue}>{ride.paymentMethod?.toUpperCase() || 'CARD'}</Text>
              </View>
              {ride.transferType && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Transfer Type:</Text>
                  <Text style={[styles.detailValue, styles.transferType]}>
                    {ride.transferType.toUpperCase()}
                  </Text>
                </View>
              )}
              {ride.hasMeetAndGreet && (
                <View style={styles.meetGreetContainer}>
                  <MaterialIcons name="assignment-ind" size={16} color="#34C759" />
                  <Text style={styles.meetGreetText}>Meet & Greet Service</Text>
                </View>
              )}
            </View>

            {ride.specialRequests && (
              <View style={styles.specialRequests}>
                <Text style={styles.specialRequestsLabel}>Special Requests:</Text>
                <Text style={styles.specialRequestsText}>{ride.specialRequests}</Text>
              </View>
            )}

            {showActions && (
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.callButton}
                  onPress={() => handleCallPassenger(ride.passengerPhone, ride.passengerName)}
                  activeOpacity={0.7}
                >
                  <MaterialIcons name="phone" size={16} color="#FFFFFF" />
                  <Text style={styles.actionButtonText}>Call</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.messageButton}
                  onPress={() => handleMessagePassenger(ride.passengerPhone, ride.passengerName)}
                  activeOpacity={0.7}
                >
                  <MaterialIcons name="message" size={16} color="#FFFFFF" />
                  <Text style={styles.actionButtonText}>Message</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    marginVertical: 8,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  expandedContent: {
    backgroundColor: '#F8F9FA',
    padding: 16,
  },
  customerInfo: {
    gap: 12,
  },
  customerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  customerLeft: {
    flex: 2,
  },
  customerRight: {
    flex: 1,
    alignItems: 'flex-end',
    gap: 6,
  },
  customerName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  customerPhone: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 4,
    fontWeight: '500',
  },
  customerCount: {
    fontSize: 12,
    color: '#6D6D70',
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#FF9500',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    textAlign: 'center',
  },
  tripDetails: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: '#6D6D70',
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    color: '#1D1D1F',
    fontWeight: '600',
  },
  transferType: {
    color: '#007AFF',
  },
  meetGreetContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
    alignSelf: 'flex-start',
  },
  meetGreetText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#34C759',
  },
  specialRequests: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#FF9500',
  },
  specialRequestsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  specialRequestsText: {
    fontSize: 14,
    color: '#1D1D1F',
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#34C759',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  messageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default ExpandableCustomerDetails;