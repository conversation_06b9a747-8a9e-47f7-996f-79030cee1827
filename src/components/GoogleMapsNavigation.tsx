import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import MapVie<PERSON>, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

interface NavigationProps {
  origin: { latitude: number; longitude: number };
  destination: { latitude: number; longitude: number };
  onNavigationProgress?: (progress: any) => void;
}

interface RouteCoordinates {
  latitude: number;
  longitude: number;
}

export const GoogleMapsNavigation: React.FC<NavigationProps> = ({
  origin,
  destination,
  onNavigationProgress
}) => {
  const [routeCoordinates, setRouteCoordinates] = useState<RouteCoordinates[]>([]);
  const [mapRegion, setMapRegion] = useState({
    latitude: origin.latitude,
    longitude: origin.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  // Get directions using Google Directions API
  const getDirections = async () => {
    try {
      const apiKey = 'AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ'; // Your Google Maps API key
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${apiKey}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.routes.length > 0) {
        const points = data.routes[0].overview_polyline.points;
        const decodedPoints = decodePolyline(points);
        setRouteCoordinates(decodedPoints);
        
        // Calculate region to fit both points
        const minLat = Math.min(origin.latitude, destination.latitude);
        const maxLat = Math.max(origin.latitude, destination.latitude);
        const minLng = Math.min(origin.longitude, destination.longitude);
        const maxLng = Math.max(origin.longitude, destination.longitude);
        
        setMapRegion({
          latitude: (minLat + maxLat) / 2,
          longitude: (minLng + maxLng) / 2,
          latitudeDelta: (maxLat - minLat) * 1.5,
          longitudeDelta: (maxLng - minLng) * 1.5,
        });
      }
    } catch (error) {
      console.error('Error fetching directions:', error);
      // Fallback: direct line between points
      setRouteCoordinates([origin, destination]);
    }
  };

  // Simple polyline decoder (simplified version)
  const decodePolyline = (encoded: string): RouteCoordinates[] => {
    // This is a simplified decoder for Google polylines
    const coordinates: RouteCoordinates[] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      coordinates.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return coordinates;
  };

  useEffect(() => {
    getDirections();
  }, [origin, destination]);

  return (
    <View style={styles.container}>
      <MapView
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={mapRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
        showsTraffic={true}
        onRegionChangeComplete={setMapRegion}
      >
        {/* Driver location marker */}
        <Marker
          coordinate={origin}
          title="Driver Location"
          description="Your current location"
          pinColor="green"
        />

        {/* Customer location marker */}
        <Marker
          coordinate={destination}
          title="Customer Location"
          description="Pick up location"
          pinColor="red"
        />

        {/* Route polyline */}
        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeColor="#3b82f6"
            strokeWidth={4}
            strokePattern={[1]}
          />
        )}
      </MapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

export default GoogleMapsNavigation;