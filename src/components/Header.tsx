import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { shadows } from '../theme/colors';
import { useTheme } from '../context/ThemeContext';

interface HeaderProps {
  title: string;
  subtitle?: string;
  onMenuPress: () => void;
  rightComponent?: React.ReactNode;
  backgroundColor?: string;
  showThemeToggle?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  onMenuPress,
  rightComponent,
  backgroundColor,
  showThemeToggle = false
}) => {
  const { colors, isDarkMode, toggleTheme } = useTheme();
  const headerBgColor = backgroundColor || colors.primary;
  return (
    <SafeAreaView edges={['top']} style={[styles.safeArea, { backgroundColor: headerBgColor }]}>
      <View style={[styles.header, { backgroundColor: headerBgColor }]}>
        <TouchableOpacity style={styles.menuButton} onPress={onMenuPress}>
          <MaterialIcons name="menu" size={24} color={colors.textLight} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Image 
              source={require('../../assets/logo.png')} 
              style={styles.logo} 
              resizeMode="contain"
            />
            <Text style={[styles.headerTitle, { color: colors.textLight }]}>{title}</Text>
          </View>
          {subtitle && (
            <Text style={[styles.headerSubtitle, { color: colors.textLight }]}>{subtitle}</Text>
          )}
        </View>
        
        <View style={styles.rightSection}>
          {showThemeToggle && (
            <TouchableOpacity style={styles.themeToggle} onPress={toggleTheme}>
              <MaterialIcons 
                name={isDarkMode ? "light-mode" : "dark-mode"} 
                size={24} 
                color={colors.textLight} 
              />
            </TouchableOpacity>
          )}
          {rightComponent}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {},
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 16,
    ...shadows.medium,
  },
  menuButton: {
    padding: 8,
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 30,
    height: 30,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerSubtitle: {
    fontSize: 14,
    opacity: 0.8,
    marginTop: 2,
  },
  rightSection: {
    minWidth: 40,
    alignItems: 'flex-end',
    flexDirection: 'row',
    gap: 8,
  },
  themeToggle: {
    padding: 8,
  },
});

export default Header;