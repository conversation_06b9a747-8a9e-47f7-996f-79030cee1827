import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { colors, shadows } from '../theme/colors';
import { navigateToTab } from '../services/AppNavigationService';

interface IslandBottomNavigationProps {
  activeTab: string;
  onTabPress: (tab: string) => void;
  visible: boolean;
}

const IslandBottomNavigation: React.FC<IslandBottomNavigationProps> = ({
  activeTab,
  onTabPress,
  visible,
}) => {
  // Use useRef to persist the animated value between renders
  const animatedValue = React.useRef(new Animated.Value(visible ? 1 : 0)).current;
  
  // Keep track of the last tab to prevent unnecessary re-renders
  const lastTabRef = React.useRef(activeTab);

  React.useEffect(() => {
    // Animate the bottom navigation in or out when visibility changes
    Animated.timing(animatedValue, {
      toValue: visible ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [visible, animatedValue]);
  
  // Update the last tab reference
  React.useEffect(() => {
    lastTabRef.current = activeTab;
  }, [activeTab]);

  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'dashboard' as const,
      activeColor: colors.primary,
      inactiveColor: colors.navigationInactive,
    },
    {
      id: 'bookings',
      label: 'Bookings',
      icon: 'assignment' as const,
      activeColor: colors.primary,
      inactiveColor: colors.navigationInactive,
    },
    {
      id: 'earnings',
      label: 'Earnings',
      icon: 'wallet' as const,
      activeColor: colors.primary,
      inactiveColor: colors.navigationInactive,
    },
  ];

  // Don't render at all if not visible
  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: animatedValue,
          transform: [
            {
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [100, 0],
              }),
            },
          ],
          // Ensure it doesn't take up space or receive touches when hidden
          pointerEvents: visible ? 'auto' : 'none',
        },
      ]}
    >
      <View style={styles.islandContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              activeTab === tab.id && styles.activeTabButton,
            ]}
            onPress={() => {
              // Just use the local navigation prop
              onTabPress(tab.id);
            }}
            activeOpacity={0.7}
          >
            <View style={styles.tabContent}>
              <MaterialIcons
                name={tab.icon}
                size={24}
                color={activeTab === tab.id ? tab.activeColor : tab.inactiveColor}
              />
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
    alignItems: 'center',
    zIndex: 1000,
  },
  islandContainer: {
    flexDirection: 'row',
    backgroundColor: colors.cardBackground,
    borderRadius: 25,
    paddingVertical: 8,
    paddingHorizontal: 12,
    ...shadows.large,
    borderWidth: 1,
    borderColor: colors.border,
    maxWidth: '70%',
    justifyContent: 'space-between',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    minWidth: 30,
  },
  activeTabButton: {
    backgroundColor: colors.activeBackground,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
});

export default IslandBottomNavigation;