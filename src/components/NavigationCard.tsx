import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import TransferNavigationModal from './TransferNavigationModal';

interface NavigationCardProps {
  destination?: {
    latitude: number;
    longitude: number;
    name: string;
  };
  onNavigationComplete?: () => void;
}

export const NavigationCard: React.FC<NavigationCardProps> = ({
  destination,
  onNavigationComplete
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<string>('');
  const [estimatedDistance, setEstimatedDistance] = useState<string>('');
  
  // Default destination (can be overridden by props)
  const defaultDestination = {
    latitude: 14.5995,
    longitude: 120.9842,
    name: 'Manila City'
  };
  
  const actualDestination = destination || defaultDestination;

  // Get current location and estimate travel time
  useEffect(() => {
    const getLocationAndEstimate = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          return;
        }

        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced
        });

        const currentPos = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        };
        
        setCurrentLocation(currentPos);
        
        // Get estimated time and distance
        await getEstimatedTimeAndDistance(currentPos, actualDestination);
      } catch (error) {
        console.error('Error getting location:', error);
      }
    };

    getLocationAndEstimate();
  }, [destination]);

  // Get estimated time and distance
  const getEstimatedTimeAndDistance = async (
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number }
  ) => {
    try {
      const apiKey = 'AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ';
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${apiKey}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        const leg = route.legs[0];
        
        setEstimatedTime(leg.duration.text);
        setEstimatedDistance(leg.distance.text);
      }
    } catch (error) {
      console.error('Error getting directions:', error);
    }
  };

  // Open navigation modal
  const openNavigationModal = () => {
    setModalVisible(true);
  };

  // Close navigation modal
  const closeNavigationModal = () => {
    setModalVisible(false);
  };

  // Handle navigation completion
  const handleNavigationComplete = () => {
    onNavigationComplete?.();
  };

  return (
    <>
      <TouchableOpacity style={styles.card} onPress={openNavigationModal}>
        <View style={styles.cardHeader}>
          <MaterialIcons name="navigation" size={24} color="#4A7C59" />
          <Text style={styles.cardTitle}>Navigation</Text>
        </View>
        
        <View style={styles.destinationContainer}>
          <View style={styles.destinationInfo}>
            <Text style={styles.destinationName}>{actualDestination.name}</Text>
            <View style={styles.estimateContainer}>
              {estimatedTime ? (
                <>
                  <MaterialIcons name="access-time" size={16} color="#666" />
                  <Text style={styles.estimateText}>{estimatedTime}</Text>
                  <MaterialIcons name="straighten" size={16} color="#666" style={styles.distanceIcon} />
                  <Text style={styles.estimateText}>{estimatedDistance}</Text>
                </>
              ) : (
                <Text style={styles.estimateText}>Calculating...</Text>
              )}
            </View>
          </View>
          
          <View style={styles.navigationButton}>
            <MaterialIcons name="directions" size={24} color="#fff" />
          </View>
        </View>
        
        <View style={styles.mapPreview}>
          <Image
            source={{ 
              uri: `https://maps.googleapis.com/maps/api/staticmap?center=${actualDestination.latitude},${actualDestination.longitude}&zoom=13&size=400x200&maptype=roadmap&markers=color:red%7C${actualDestination.latitude},${actualDestination.longitude}&key=AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ&style=feature:all|element:labels|visibility:on` 
            }}
            style={styles.mapImage}
            resizeMode="cover"
          />
        </View>
      </TouchableOpacity>
      
      <TransferNavigationModal
        visible={modalVisible}
        onClose={closeNavigationModal}
        destination={actualDestination}
        onNavigationComplete={handleNavigationComplete}
      />
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  destinationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  destinationInfo: {
    flex: 1,
  },
  destinationName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  estimateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  estimateText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  distanceIcon: {
    marginLeft: 12,
  },
  navigationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4A7C59',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mapPreview: {
    height: 150,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
});

export default NavigationCard;