import React, { useState } from 'react';
import { Modal, Alert, View, StyleSheet } from 'react-native';
import EnhancedWazeNavigator from './EnhancedWazeNavigator';
import SpeedSimulator from './SpeedSimulator';

interface NavigationModalManagerProps {
  visible: boolean;
  onClose: () => void;
  destination: {
    latitude: number;
    longitude: number;
    name?: string;
  };
}

const NavigationModalManager: React.FC<NavigationModalManagerProps> = ({
  visible,
  onClose,
  destination
}) => {
  const [isNavigating, setIsNavigating] = useState(false);
  
  const handleArrival = () => {
    setIsNavigating(false);
    onClose();
    Alert.alert('Navigation Complete', 'You have successfully arrived at your destination!');
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <EnhancedWazeNavigator
          destination={{
            latitude: destination.latitude,
            longitude: destination.longitude
          }}
          destinationName={destination.name || 'Destination'}
          onClose={onClose}
          onArrival={handleArrival}
        />
        
        {/* Speed Indicator */}
        <SpeedSimulator 
          isActive={visible} 
          style={styles.speedIndicator}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  speedIndicator: {
    position: 'absolute',
    bottom: 140,
    left: 20,
  },
});

export default NavigationModalManager;