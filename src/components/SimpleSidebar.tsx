import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';
import { colors, shadows } from '../theme/colors';

type MaterialIconName = 
  | 'dashboard' 
  | 'map'
  | 'assignment' 
  | 'attach-money' 
  | 'person' 
  | 'directions-car' 
  | 'settings' 
  | 'help' 
  | 'logout'
  | 'close';

interface MenuItem {
  id: string;
  title: string;
  icon: MaterialIconName;
  badge?: number;
}

interface SimpleSidebarProps {
  visible: boolean;
  onClose: () => void;
  onNavigate: (screen: string) => void;
  currentScreen: string;
}

const SimpleSidebar = React.memo<SimpleSidebarProps>(({ visible, onClose, onNavigate, currentScreen }) => {
  const { state: authState } = useAuth();
  const { state: appState } = useApp();

  // Calculate today's earnings
  const todayEarnings = React.useMemo(() => {
    if (!appState.trips || appState.trips.length === 0) {
      return '0.00';
    }
    
    try {
      const today = new Date();
      const todayTotal = appState.trips
        .filter(trip => {
          if (!trip.completedAt) return false;
          const tripDate = new Date(trip.completedAt);
          return tripDate.toDateString() === today.toDateString();
        })
        .reduce((sum, trip) => sum + trip.earnings, 0);
      
      return todayTotal.toFixed(2);
    } catch (error) {
      console.error('Error calculating earnings:', error);
      return '0.00';
    }
  }, [appState.trips]);

  const mainMenuItems: MenuItem[] = [
    { id: 'dashboard', title: 'Dashboard', icon: 'dashboard' },
    { id: 'bookings', title: 'Bookings', icon: 'assignment', badge: appState.pendingBookings?.length || 0 },
    { id: 'earnings', title: 'Earnings', icon: 'wallet' },
  ];

  const additionalMenuItems: MenuItem[] = [
    { id: 'profile', title: 'Profile', icon: 'person' },
    { id: 'help', title: 'Help & Support', icon: 'help' },
  ];

  const handleItemPress = (screenId: string) => {
    onNavigate(screenId);
  };

  const renderMenuItem = (item: MenuItem) => {
    const isActive = currentScreen === item.id;
    
    return (
      <TouchableOpacity
        key={item.id}
        style={[styles.menuItem, isActive && styles.activeMenuItem]}
        onPress={() => handleItemPress(item.id)}
      >
        <View style={styles.menuItemContent}>
          <View style={styles.menuItemLeft}>
            <MaterialIcons 
              name={item.icon} 
              size={20} 
              color={isActive ? colors.navigationActive : colors.navigationInactive} 
              style={styles.menuItemIcon}
            />
            <Text style={[styles.menuItemText, isActive && styles.activeMenuItemText]}>
              {item.title}
            </Text>
          </View>
          {item.badge && item.badge > 0 ? (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{String(item.badge)}</Text>
            </View>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      onRequestClose={onClose}
      animationType="none"
    >
      <View style={styles.overlay}>
        <SafeAreaView style={styles.sidebar}>
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Image 
                source={require('../../assets/logo.png')} 
                style={styles.logo} 
                resizeMode="contain"
              />
            </View>
            <View style={styles.driverInfo}>
              <Text style={styles.driverName}>{authState.driver?.name || 'Driver'}</Text>
              <Text style={styles.driverRating}>Online</Text>
              <View style={styles.statusIndicator}>
                <View style={[styles.statusDot, { backgroundColor: colors.success }]} />
                <Text style={styles.statusText}>Available</Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.menu}>
            {mainMenuItems.map(renderMenuItem)}
            
            <View style={styles.menuSeparator} />
            
            {additionalMenuItems.map(renderMenuItem)}
          </View>

          <View style={styles.footer}>
            <View style={styles.todayStats}>
              <Text style={styles.footerTitle}>Today's Stats</Text>
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{String(appState.todayTrips || 0)}</Text>
                  <Text style={styles.statLabel}>Trips</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>£{todayEarnings}</Text>
                  <Text style={styles.statLabel}>Earnings</Text>
                </View>
              </View>
            </View>
          </View>
        </SafeAreaView>
        
        <TouchableOpacity 
          style={styles.overlayBackground} 
          onPress={onClose}
          activeOpacity={1}
        />
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: 'row',
  },
  overlayBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sidebar: {
    width: 280,
    backgroundColor: colors.background,
    ...shadows.medium,
  },
  header: {
    flexDirection: 'column',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.primary,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  logo: {
    width: 120,
    height: 60,
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.textLight,
    marginBottom: 4,
  },
  driverRating: {
    fontSize: 14,
    color: colors.textLight,
    fontWeight: '500',
    marginBottom: 8,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: colors.textLight,
    fontWeight: '500',
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menu: {
    flex: 1,
    paddingVertical: 20,
  },
  menuItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 0,
  },
  activeMenuItem: {
    backgroundColor: colors.activeBackground,
    borderRightWidth: 3,
    borderRightColor: colors.navigationActive,
  },
  menuItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemIcon: {
    marginRight: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text,
    fontWeight: '500',
  },
  activeMenuItemText: {
    color: colors.navigationActive,
    fontWeight: '600',
  },
  badge: {
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    color: colors.textLight,
    fontSize: 12,
    fontWeight: 'bold',
  },
  menuSeparator: {
    height: 1,
    backgroundColor: colors.border,
    marginHorizontal: 20,
    marginVertical: 8,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  todayStats: {
    alignItems: 'center',
  },
  footerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.success,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 2,
  },
});

export default SimpleSidebar;