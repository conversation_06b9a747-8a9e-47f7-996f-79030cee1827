import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import * as Location from 'expo-location';
import { NavigationService } from '../services/NavigationService';

interface SpeedSimulatorProps {
  isActive: boolean;
  style?: any;
  destination?: { latitude: number; longitude: number };
}

const SpeedSimulator: React.FC<SpeedSimulatorProps> = ({ isActive, style, destination }) => {
  const [speed, setSpeed] = useState(0);
  const [previousLocation, setPreviousLocation] = useState<Location.LocationObject | null>(null);
  const [previousTimestamp, setPreviousTimestamp] = useState<number | null>(null);
  const [speedHistory, setSpeedHistory] = useState<number[]>([0, 0, 0, 0, 0]); // Keep last 5 speed readings
  const apiUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate distance between two points in meters
  const calculateDistance = (
    lat1: number, 
    lon1: number, 
    lat2: number, 
    lon2: number
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Calculate speed based on distance and time
  const calculateSpeedFromPositions = (
    currentLocation: Location.LocationObject,
    previousLocation: Location.LocationObject,
    currentTimestamp: number,
    previousTimestamp: number
  ): number => {
    // Calculate distance in meters
    const distance = calculateDistance(
      previousLocation.coords.latitude,
      previousLocation.coords.longitude,
      currentLocation.coords.latitude,
      currentLocation.coords.longitude
    );

    // Calculate time difference in seconds
    const timeDiff = (currentTimestamp - previousTimestamp) / 1000;
    
    // If time difference is too small, return previous speed to avoid division by near-zero
    if (timeDiff < 0.1) return 0;
    
    // Calculate speed in m/s
    const speedMps = distance / timeDiff;
    
    // Convert to km/h and apply minimum threshold to avoid flickering
    const speedKmh = speedMps * 3.6;
    
    // Return 0 if speed is very low to prevent flickering between 0 and small values
    return speedKmh < 3 ? 0 : speedKmh;
  };

  useEffect(() => {
    let locationSubscription: any = null;
    let speedUpdateInterval: NodeJS.Timeout | null = null;
    const navigationService = NavigationService.getInstance();
    
    // Keep track of last stable speed to prevent flickering
    let lastStableSpeed = 0;
    let stableSpeedCounter = 0;

    const startSpeedTracking = async () => {
      try {
        // Request permissions if not already granted
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          console.log('Location permission denied');
          return;
        }

        // Start watching position with high accuracy
        locationSubscription = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.BestForNavigation,
            timeInterval: 2000, // Update every 2 seconds to reduce flickering
            distanceInterval: 5, // Update every 5 meters to reduce noise
          },
          async (location) => {
            const currentTimestamp = new Date().getTime();
            const currentLocation = {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude
            };
            
            // Get raw speed value
            let rawSpeedKmh = 0;
            
            // PRIORITY 1: Use Google's traffic data if destination is provided
            // This is the most accurate source of speed data
            if (destination) {
              try {
                // First try to get real-time speed from the API
                const googleSpeedEstimate = await navigationService.getCurrentSpeedEstimate(
                  currentLocation,
                  destination,
                  previousLocation ? {
                    latitude: previousLocation.coords.latitude,
                    longitude: previousLocation.coords.longitude
                  } : undefined,
                  previousTimestamp || undefined
                );
                
                if (googleSpeedEstimate !== null) {
                  rawSpeedKmh = googleSpeedEstimate;
                }
              } catch (error) {
                console.error('Error getting Google speed estimate:', error);
                // Will fall back to other methods
              }
            }
            
            // PRIORITY 2: Use native speed from GPS if available and Google data isn't
            if (rawSpeedKmh === 0 && location.coords.speed !== null && location.coords.speed >= 0) {
              // Convert m/s to km/h
              rawSpeedKmh = location.coords.speed * 3.6;
            } 
            
            // PRIORITY 3: Calculate speed from position changes as final fallback
            if (rawSpeedKmh === 0 && previousLocation && previousTimestamp) {
              rawSpeedKmh = calculateSpeedFromPositions(
                location,
                previousLocation,
                currentTimestamp,
                previousTimestamp
              );
              
              // Apply a threshold to filter out small movements when stationary
              if (rawSpeedKmh < 3) rawSpeedKmh = 0;
            }
            
            // Apply enhanced smoothing algorithm using moving average
            const newSpeedHistory = [...speedHistory.slice(1), rawSpeedKmh];
            setSpeedHistory(newSpeedHistory);
            
            // Calculate average speed from history with improved weighting
            // More weight to recent readings but still some smoothing
            const weights = [0.05, 0.10, 0.15, 0.30, 0.40]; 
            const weightedSum = newSpeedHistory.reduce((sum, value, index) => sum + value * weights[index], 0);
            
            // Check if device is stationary with improved detection
            const isStationary = (
              // Either very low speed from GPS
              (location.coords.speed !== null && location.coords.speed * 3.6 < 3) ||
              // Or minimal movement between position updates with poor accuracy
              (location.coords.accuracy > 15 && 
               previousLocation && 
               calculateDistance(
                 previousLocation.coords.latitude,
                 previousLocation.coords.longitude,
                 location.coords.latitude,
                 location.coords.longitude
               ) < Math.max(5, location.coords.accuracy / 3))
            );
            
            // Calculate the smoothed speed value
            let smoothedSpeed = isStationary ? 0 : Math.round(weightedSum);
            
            // Anti-flicker logic: If the new speed is close to the last stable speed, keep the stable speed
            if (Math.abs(smoothedSpeed - lastStableSpeed) < 3) {
              stableSpeedCounter++;
              if (stableSpeedCounter > 2) {
                // Only update stable speed after multiple consistent readings
                lastStableSpeed = smoothedSpeed;
              } else {
                // Use the stable speed to prevent small fluctuations
                smoothedSpeed = lastStableSpeed;
              }
            } else {
              // Reset counter when speed changes significantly
              stableSpeedCounter = 0;
              lastStableSpeed = smoothedSpeed;
            }
            
            // Set the final speed value
            setSpeed(smoothedSpeed);
            
            // Store current location and timestamp for next calculation
            setPreviousLocation(location);
            setPreviousTimestamp(currentTimestamp);
          }
        );
        
        // Set up a periodic update for Google's traffic data
        if (destination) {
          speedUpdateInterval = setInterval(async () => {
            if (previousLocation) {
              const currentLocation = {
                latitude: previousLocation.coords.latitude,
                longitude: previousLocation.coords.longitude
              };
              
              try {
                // Get real-time traffic data from Google Maps API
                const eta = await navigationService.getTrafficAwareETA(currentLocation, destination);
                if (eta?.averageSpeed) {
                  // Update speed history with Google's estimate (more accurate)
                  const newSpeedHistory = [...speedHistory.slice(1), eta.averageSpeed];
                  setSpeedHistory(newSpeedHistory);
                  
                  // Immediately update speed with a blend of current and API data
                  // Heavily weight the API data for accuracy
                  const blendedSpeed = Math.round((eta.averageSpeed * 0.8) + (speed * 0.2));
                  setSpeed(blendedSpeed);
                }
              } catch (error) {
                console.error('Error updating speed from Google data:', error);
              }
            }
          }, 20000); // Update every 20 seconds - balance between accuracy and API usage
        }
      } catch (error) {
        console.error('Error tracking speed:', error);
        setSpeed(0);
      }
    };

    if (isActive) {
      startSpeedTracking();
    }

    return () => {
      if (locationSubscription) {
        locationSubscription.remove && locationSubscription.remove();
      }
      if (speedUpdateInterval) {
        clearInterval(speedUpdateInterval);
      }
    };
  }, [isActive, previousLocation, previousTimestamp, destination, speedHistory, speed]);

  // Get color based on speed
  const getSpeedColor = (speed: number): string => {
    if (speed === 0) return '#666'; // Gray when stopped
    if (speed < 30) return '#4CAF50'; // Green for slow speeds
    if (speed < 60) return '#2196F3'; // Blue for medium speeds
    if (speed < 90) return '#FF9800'; // Orange for high speeds
    return '#F44336'; // Red for very high speeds
  };
  
  // Format speed to avoid decimal places and ensure stability
  const formattedSpeed = Math.max(0, Math.round(speed));
  
  // Update speed periodically from Google's traffic data
  useEffect(() => {
    const updateGoogleSpeed = async () => {
      if (!isActive || !destination || !previousLocation) return;
      
      try {
        const navigationService = NavigationService.getInstance();
        const currentLocation = {
          latitude: previousLocation.coords.latitude,
          longitude: previousLocation.coords.longitude
        };
        
        // Get real-time speed data from Google Maps API
        const eta = await navigationService.getTrafficAwareETA(currentLocation, destination);
        if (eta?.averageSpeed) {
          // Blend Google's speed estimate with our current speed for smoother transitions
          const googleSpeed = eta.averageSpeed;
          
          // Use a higher weight for Google's data (80%) for more accuracy
          const blendedSpeed = Math.round((googleSpeed * 0.8) + (speed * 0.2));
          setSpeed(blendedSpeed);
          
          // Update speed history with this more accurate reading
          const newSpeedHistory = [...speedHistory.slice(1), blendedSpeed];
          setSpeedHistory(newSpeedHistory);
        }
      } catch (error) {
        console.error('Error updating Google speed data:', error);
      }
    };
    
    if (isActive && destination) {
      // Initial update
      updateGoogleSpeed();
      
      // Clear any existing interval
      if (apiUpdateIntervalRef.current) {
        clearInterval(apiUpdateIntervalRef.current);
      }
      
      // Set interval for periodic updates - more frequent updates (every 10 seconds)
      apiUpdateIntervalRef.current = setInterval(updateGoogleSpeed, 10000);
    }
    
    return () => {
      if (apiUpdateIntervalRef.current) {
        clearInterval(apiUpdateIntervalRef.current);
        apiUpdateIntervalRef.current = null;
      }
    };
  }, [isActive, destination, previousLocation, speed, speedHistory]);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.innerContainer}>
        <Text style={[styles.speedValue, { color: getSpeedColor(formattedSpeed) }]}>
          {formattedSpeed}
        </Text>
        <Text style={styles.speedUnit}>km/h</Text>
      </View>
      <Text style={styles.label}>Speed</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  innerContainer: {
    alignItems: 'center',
  },
  speedValue: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  speedUnit: {
    fontSize: 10,
    color: '#666',
    marginTop: -2,
  },
  label: {
    fontSize: 9,
    color: '#999',
    marginTop: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  }
});

// Export both named and default
export { SpeedSimulator };
export default SpeedSimulator;