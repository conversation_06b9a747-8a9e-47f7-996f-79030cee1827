import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, ScrollView } from 'react-native';
import Button from './Button';
import { Ride } from '../types';

interface TimePreferenceModalProps {
  visible: boolean;
  onClose: () => void;
  booking: Ride | null;
  onAccept: (booking: Ride) => void;
}

const TimePreferenceModal: React.FC<TimePreferenceModalProps> = ({ 
  visible, 
  onClose, 
  booking,
  onAccept 
}) => {
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);

  if (!booking) return null;

  const timeSlots = [
    { id: 'now', label: 'Now', time: 'Immediate pickup' },
    { id: '15min', label: '15 minutes', time: new Date(Date.now() + 15 * 60000).toLocaleTimeString() },
    { id: '30min', label: '30 minutes', time: new Date(Date.now() + 30 * 60000).toLocaleTimeString() },
    { id: '1hour', label: '1 hour', time: new Date(Date.now() + 60 * 60000).toLocaleTimeString() },
    { id: '2hours', label: '2 hours', time: new Date(Date.now() + 120 * 60000).toLocaleTimeString() },
  ];

  const preferredTimes = [
    { id: 'morning', label: 'Morning (6-12 PM)', icon: '🌅' },
    { id: 'afternoon', label: 'Afternoon (12-6 PM)', icon: '☀️' },
    { id: 'evening', label: 'Evening (6-10 PM)', icon: '🌆' },
    { id: 'night', label: 'Night (10 PM-6 AM)', icon: '🌙' },
  ];

  const handleAccept = () => {
    const updatedBooking = {
      ...booking,
      scheduledTime: selectedTimeSlot ? new Date(Date.now() + parseInt(selectedTimeSlot) * 60000) : undefined,
    };
    onAccept(updatedBooking);
    onClose();
  };

  const formatScheduledTime = (scheduledTime: Date | undefined) => {
    if (!scheduledTime) return 'Not scheduled';
    const now = new Date();
    const timeDiff = scheduledTime.getTime() - now.getTime();
    const minutesDiff = Math.floor(timeDiff / 60000);
    
    if (minutesDiff < 60) {
      return `in ${minutesDiff} minutes`;
    } else if (minutesDiff < 1440) {
      return `in ${Math.floor(minutesDiff / 60)} hours`;
    } else {
      return scheduledTime.toLocaleDateString();
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Booking Time Preferences</Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.bookingInfo}>
            <Text style={styles.passengerName}>{booking.passengerName}</Text>
            <Text style={styles.bookingRoute}>
              📍 {booking.pickupLocation.address}
            </Text>
            <Text style={styles.bookingRoute}>
              🏁 {booking.dropoffLocation.address}
            </Text>
            <Text style={styles.fareAmount}>💰 £{booking.fare.toFixed(2)}</Text>
          </View>

          {booking.scheduledTime && (
            <View style={styles.scheduledSection}>
              <Text style={styles.sectionTitle}>📅 Scheduled Pickup</Text>
              <View style={styles.scheduledTimeCard}>
                <Text style={styles.scheduledTime}>
                  {booking.scheduledTime.toLocaleString()}
                </Text>
                <Text style={styles.scheduledTimeRelative}>
                  {formatScheduledTime(booking.scheduledTime)}
                </Text>
              </View>
            </View>
          )}

          {booking.preferredTime && (
            <View style={styles.preferredSection}>
              <Text style={styles.sectionTitle}>🕒 Passenger Prefers</Text>
              <View style={styles.preferredTimeCard}>
                {preferredTimes.map(time => (
                  time.id === booking.preferredTime && (
                    <View key={time.id} style={styles.preferredTimeItem}>
                      <Text style={styles.preferredTimeIcon}>{time.icon}</Text>
                      <Text style={styles.preferredTimeLabel}>{time.label}</Text>
                    </View>
                  )
                ))}
              </View>
            </View>
          )}

          <View style={styles.timeSlotSection}>
            <Text style={styles.sectionTitle}>⏰ When can you pick up?</Text>
            {timeSlots.map(slot => (
              <TouchableOpacity
                key={slot.id}
                style={[
                  styles.timeSlotItem,
                  selectedTimeSlot === slot.id && styles.selectedTimeSlot
                ]}
                onPress={() => setSelectedTimeSlot(slot.id)}
              >
                <View style={styles.timeSlotContent}>
                  <Text style={[
                    styles.timeSlotLabel,
                    selectedTimeSlot === slot.id && styles.selectedTimeSlotText
                  ]}>
                    {slot.label}
                  </Text>
                  <Text style={[
                    styles.timeSlotTime,
                    selectedTimeSlot === slot.id && styles.selectedTimeSlotText
                  ]}>
                    {slot.time}
                  </Text>
                </View>
                {selectedTimeSlot === slot.id && (
                  <Text style={styles.checkmark}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>

          {booking.specialRequests && (
            <View style={styles.specialRequestsSection}>
              <Text style={styles.sectionTitle}>💬 Special Requests</Text>
              <Text style={styles.specialRequestsText}>{booking.specialRequests}</Text>
            </View>
          )}

          <View style={styles.prioritySection}>
            <Text style={styles.sectionTitle}>🚨 Priority Level</Text>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(booking.priority) }]}>
              <Text style={styles.priorityText}>{booking.priority.toUpperCase()} PRIORITY</Text>
            </View>
          </View>
        </ScrollView>

        <View style={styles.actions}>
          <Button
            title="Decline"
            onPress={onClose}
            variant="secondary"
            style={styles.actionButton}
          />
          <Button
            title="Accept Booking"
            onPress={handleAccept}
            variant="success"
            style={styles.actionButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return '#FF3B30';
    case 'medium': return '#FF9500';
    case 'low': return '#34C759';
    default: return '#6D6D70';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  closeButton: {
    fontSize: 18,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  bookingInfo: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  passengerName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 8,
  },
  bookingRoute: {
    fontSize: 14,
    color: '#6D6D70',
    marginBottom: 4,
  },
  fareAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34C759',
    marginTop: 8,
  },
  scheduledSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 12,
  },
  scheduledTimeCard: {
    backgroundColor: '#FFF3E0',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FF9500',
  },
  scheduledTime: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  scheduledTimeRelative: {
    fontSize: 14,
    color: '#FF9500',
    marginTop: 4,
  },
  preferredSection: {
    marginBottom: 20,
  },
  preferredTimeCard: {
    backgroundColor: '#F0F8FF',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  preferredTimeItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  preferredTimeIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  preferredTimeLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1D1D1F',
  },
  timeSlotSection: {
    marginBottom: 20,
  },
  timeSlotItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  selectedTimeSlot: {
    backgroundColor: '#E8F5E8',
    borderColor: '#34C759',
  },
  timeSlotContent: {
    flex: 1,
  },
  timeSlotLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  timeSlotTime: {
    fontSize: 14,
    color: '#6D6D70',
    marginTop: 2,
  },
  selectedTimeSlotText: {
    color: '#34C759',
  },
  checkmark: {
    fontSize: 20,
    color: '#34C759',
    fontWeight: 'bold',
  },
  specialRequestsSection: {
    marginBottom: 20,
  },
  specialRequestsText: {
    fontSize: 14,
    color: '#1D1D1F',
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    fontStyle: 'italic',
  },
  prioritySection: {
    marginBottom: 20,
  },
  priorityBadge: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  actions: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default TimePreferenceModal;