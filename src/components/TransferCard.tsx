import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { colors, shadows } from '../theme/colors';
import { Ride } from '../types';

interface TransferCardProps {
  ride: Ride;
  onPress?: () => void;
  showActions?: boolean;
}

const TransferCard: React.FC<TransferCardProps> = ({
  ride,
  onPress,
  showActions = false
}) => {
  const getStatusColor = () => {
    if (ride.status === 'pending' && ride.bookingStatus === 'urgent') return colors.error;
    if (ride.status === 'accepted') return colors.primary;
    if (ride.bookingStatus === 'scheduled') return colors.primary;
    if (ride.status === 'completed') return colors.success;
    if (ride.status === 'rejected') return colors.error;
    if (ride.bookingStatus === 'premium') return colors.warning;
    return colors.textSecondary;
  };

  const getStatusText = () => {
    if (ride.status === 'pending' && ride.bookingStatus === 'urgent') return 'URGENT';
    if (ride.status === 'accepted') return 'ACCEPTED';
    if (ride.bookingStatus === 'scheduled') return 'SCHEDULED';
    if (ride.status === 'completed') return 'COMPLETED';
    if (ride.status === 'rejected') return 'REJECTED';
    if (ride.bookingStatus === 'premium') return 'PREMIUM';
    if (ride.bookingStatus === 'popular') return 'POPULAR';
    return ride.bookingStatus.toUpperCase();
  };

  const getTransferIcon = () => {
    return ride.transferType === 'arrival' ? 'flight-land' : 'flight-takeoff';
  };

  const getVehicleIcon = () => {
    switch (ride.vehicleType) {
      case 'executive':
      case 'executive_people_carrier':
        return 'star';
      case 'people_carrier':
      case 'minibus_8':
        return 'groups';
      default:
        return 'directions-car';
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return '';
    return new Date(timeString).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFlightStatusColor = () => {
    switch (ride.flightStatus) {
      case 'on_time':
        return colors.success;
      case 'delayed':
        return colors.warning;
      case 'cancelled':
        return colors.error;
      case 'landed':
        return colors.primary;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        ride.status === 'rejected' && styles.rejectedCard
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Header with passenger info and status */}
      <View style={styles.header}>
        <View style={styles.passengerSection}>
          <View style={styles.avatarContainer}>
            <MaterialIcons name="person" size={24} color={colors.primary} />
          </View>
          <View style={styles.passengerInfo}>
            <Text style={styles.passengerName}>{ride.passengerName}</Text>
            <View style={styles.airplaneIconContainer}>
              <MaterialIcons name="flight" size={16} color={colors.primary} />
            </View>
            <View style={styles.passengerDetails}>
              <MaterialIcons name={getVehicleIcon()} size={14} color={colors.textSecondary} />
              <Text style={styles.passengerCount}>{ride.passengerCount} passenger{ride.passengerCount > 1 ? 's' : ''}</Text>
              {ride.hasMeetAndGreet && (
                <>
                  <Text style={styles.separator}>•</Text>
                  <MaterialIcons name="handshake" size={14} color={colors.primary} />
                  <Text style={styles.meetGreet}>Meet & Greet</Text>
                </>
              )}
            </View>
          </View>
        </View>
        <View style={styles.statusSection}>
          <View style={[styles.transferTypeBadge, 
            ride.transferType === 'arrival' ? styles.arrivalBadge : styles.departureBadge]}>
            <MaterialIcons 
              name={getTransferIcon()} 
              size={14} 
              color={ride.transferType === 'arrival' ? colors.success : colors.warning} 
            />
            <Text style={[styles.transferTypeText, 
              ride.transferType === 'arrival' ? styles.arrivalText : styles.departureText]}>
              {ride.transferType.toUpperCase()}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </View>
        </View>
      </View>

      {/* Flight Information */}
      <View style={styles.flightSection}>
        <View style={styles.flightHeader}>
          <MaterialIcons name="flight" size={18} color={colors.primary} />
          <Text style={styles.flightNumber}>{ride.flightNumber}</Text>
          <Text style={styles.airline}>• {ride.airline}</Text>
          {ride.flightStatus && (
            <View style={[styles.flightStatusBadge, { backgroundColor: getFlightStatusColor() + '20' }]}>
              <Text style={[styles.flightStatusText, { color: getFlightStatusColor() }]}>
                {ride.flightStatus.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
          )}
        </View>
        <View style={styles.flightDetails}>
          <View style={styles.flightInfo}>
            <MaterialIcons name="location-on" size={16} color={colors.textSecondary} />
            <Text style={styles.airportText}>{ride.airport}</Text>
            {ride.terminal && (
              <>
                <Text style={styles.separator}>•</Text>
                <Text style={styles.terminalText}>Terminal {ride.terminal}</Text>
              </>
            )}
          </View>
          {ride.flightTime && (
            <Text style={styles.flightTime}>
              {formatTime(ride.flightTime)}
            </Text>
          )}
        </View>
      </View>

      {/* Route Information */}
      <View style={styles.routeSection}>
        <View style={styles.routePoint}>
          <MaterialIcons name="radio-button-checked" size={12} color={colors.success} />
          <Text style={styles.routeAddress} numberOfLines={1}>
            {ride.pickupLocation.address}
          </Text>
        </View>
        <View style={styles.routeLine} />
        <View style={styles.routePoint}>
          <MaterialIcons name="place" size={12} color={colors.error} />
          <Text style={styles.routeAddress} numberOfLines={1}>
            {ride.dropoffLocation.address}
          </Text>
        </View>
      </View>

      {/* Footer with fare and actions */}
      <View style={styles.footer}>
        <View style={styles.fareSection}>
          <Text style={styles.fareAmount}>£{ride.fare.toFixed(2)}</Text>
          <Text style={styles.fareDetails}>{ride.distance} km • {ride.vehicleType.replace('_', ' ')}</Text>
        </View>
        {showActions && (
          <View style={styles.actions}>
            <TouchableOpacity style={styles.actionButton} activeOpacity={0.7}>
              <MaterialIcons name="phone" size={16} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} activeOpacity={0.7}>
              <MaterialIcons name="message" size={16} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} activeOpacity={0.7}>
              <MaterialIcons name="navigation" size={16} color={colors.primary} />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.backgroundPrimary,
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    marginHorizontal: 16,
    ...shadows.small,
  },
  rejectedCard: {
    opacity: 0.7,
    backgroundColor: colors.backgroundSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  passengerSection: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 12,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  passengerInfo: {
    flex: 1,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  airplaneIconContainer: {
    marginBottom: 4,
    alignSelf: 'flex-start',
  },
  passengerDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passengerCount: {
    fontSize: 12,
    color: colors.textSecondary,
    marginLeft: 4,
  },
  separator: {
    fontSize: 12,
    color: colors.textSecondary,
    marginHorizontal: 6,
  },
  meetGreet: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  statusSection: {
    alignItems: 'flex-end',
  },
  transferTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 6,
  },
  arrivalBadge: {
    backgroundColor: colors.success + '20',
  },
  departureBadge: {
    backgroundColor: colors.warning + '20',
  },
  transferTypeText: {
    fontSize: 10,
    fontWeight: '700',
    marginLeft: 4,
  },
  arrivalText: {
    color: colors.success,
  },
  departureText: {
    color: colors.warning,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  flightSection: {
    marginBottom: 12,
  },
  flightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  flightNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginLeft: 6,
  },
  airline: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 4,
  },
  flightStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  flightStatusText: {
    fontSize: 9,
    fontWeight: '600',
  },
  flightDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  flightInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  airportText: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '500',
    marginLeft: 4,
  },
  terminalText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  flightTime: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  routeSection: {
    marginBottom: 16,
  },
  routePoint: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  routeLine: {
    width: 1,
    height: 16,
    backgroundColor: colors.border,
    marginLeft: 5.5,
    marginVertical: 2,
  },
  routeAddress: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  fareSection: {
    flex: 1,
  },
  fareAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 2,
  },
  fareDetails: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
});

export default TransferCard;