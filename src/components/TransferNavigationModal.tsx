import React, { useState, useEffect } from 'react';
import { Modal, View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import EnhancedWazeNavigator from './EnhancedWazeNavigator';
import SpeedSimulator from './SpeedSimulator';

interface TransferNavigationModalProps {
  visible: boolean;
  onClose: () => void;
  destination?: {
    latitude: number;
    longitude: number;
    name: string;
  };
  onNavigationComplete?: () => void;
}

export const TransferNavigationModal: React.FC<TransferNavigationModalProps> = ({
  visible,
  onClose,
  destination,
  onNavigationComplete
}) => {
  const [navigationMode, setNavigationMode] = useState<'select' | 'navigate'>('select');
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  
  // Default destination (can be overridden by props)
  const defaultDestination = {
    latitude: 14.5995,
    longitude: 120.9842,
    name: 'Manila City'
  };
  
  const actualDestination = destination || defaultDestination;

  // Request location permissions
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required for navigation');
        return false;
      }
      setHasLocationPermission(true);
      return true;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  // Get current location
  const getCurrentLocation = async () => {
    try {
      if (!hasLocationPermission) {
        const granted = await requestLocationPermission();
        if (!granted) return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High
      });

      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Error', 'Could not get your current location');
    }
  };

  // Start navigation with selected app
  const startNavigation = (mode: 'waze' | 'google' | 'apple' | 'internal') => {
    if (!actualDestination) {
      Alert.alert('Error', 'No destination provided');
      return;
    }

    if (mode === 'internal') {
      // Use our internal navigation
      setNavigationMode('navigate');
    } else {
      // Use external navigation app
      openExternalNavigation(mode);
    }
  };

  // Open external navigation app
  const openExternalNavigation = async (app: 'waze' | 'google' | 'apple') => {
    try {
      const { Linking } = require('react-native');
      let url: string;

      switch (app) {
        case 'google':
          url = Platform.select({
            ios: `comgooglemaps://?daddr=${actualDestination.latitude},${actualDestination.longitude}&directionsmode=driving`,
            android: `google.navigation:q=${actualDestination.latitude},${actualDestination.longitude}&mode=d`
          }) || '';
          break;
        case 'apple':
          url = Platform.select({
            ios: `maps://?daddr=${actualDestination.latitude},${actualDestination.longitude}&dirflg=d`,
            android: `geo:${actualDestination.latitude},${actualDestination.longitude}?q=${actualDestination.latitude},${actualDestination.longitude}`
          }) || '';
          break;
        case 'waze':
          url = `waze://?ll=${actualDestination.latitude},${actualDestination.longitude}&navigate=yes`;
          break;
        default:
          return;
      }

      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        onClose();
      } else {
        // Fallback to web URL
        const webUrl = `https://maps.google.com/maps?daddr=${actualDestination.latitude},${actualDestination.longitude}&mode=driving`;
        await Linking.openURL(webUrl);
        onClose();
      }
    } catch (error) {
      console.error('Error opening navigation app:', error);
      Alert.alert('Error', 'Could not open navigation app');
    }
  };

  // Handle navigation completion
  const handleNavigationComplete = () => {
    onNavigationComplete?.();
    onClose();
  };

  // Initialize component
  useEffect(() => {
    if (visible) {
      requestLocationPermission();
      getCurrentLocation();
    } else {
      // Reset state when modal is closed
      setNavigationMode('select');
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      {navigationMode === 'select' ? (
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Navigation Options</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.destinationInfo}>
            <MaterialIcons name="location-on" size={24} color="#4A7C59" />
            <View style={styles.destinationTextContainer}>
              <Text style={styles.destinationTitle}>{actualDestination.name}</Text>
              <Text style={styles.destinationCoords}>
                {actualDestination.latitude.toFixed(6)}, {actualDestination.longitude.toFixed(6)}
              </Text>
            </View>
          </View>
          
          <View style={styles.optionsContainer}>
            <Text style={styles.optionsTitle}>Choose Navigation App</Text>
            
            <TouchableOpacity 
              style={styles.navigationOption}
              onPress={() => startNavigation('internal')}
            >
              <View style={[styles.appIconContainer, { backgroundColor: '#4A7C59' }]}>
                <MaterialIcons name="navigation" size={28} color="#fff" />
              </View>
              <View style={styles.appTextContainer}>
                <Text style={styles.appName}>Turn-by-Turn Navigation</Text>
                <Text style={styles.appDescription}>Use our built-in navigation system</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#999" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navigationOption}
              onPress={() => startNavigation('waze')}
            >
              <View style={[styles.appIconContainer, { backgroundColor: '#33CCFF' }]}>
                <MaterialIcons name="directions-car" size={28} color="#fff" />
              </View>
              <View style={styles.appTextContainer}>
                <Text style={styles.appName}>Waze</Text>
                <Text style={styles.appDescription}>Navigate with real-time traffic updates</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#999" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navigationOption}
              onPress={() => startNavigation('google')}
            >
              <View style={[styles.appIconContainer, { backgroundColor: '#4285F4' }]}>
                <MaterialIcons name="map" size={28} color="#fff" />
              </View>
              <View style={styles.appTextContainer}>
                <Text style={styles.appName}>Google Maps</Text>
                <Text style={styles.appDescription}>Navigate with Google Maps</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#999" />
            </TouchableOpacity>
            
            {Platform.OS === 'ios' && (
              <TouchableOpacity 
                style={styles.navigationOption}
                onPress={() => startNavigation('apple')}
              >
                <View style={[styles.appIconContainer, { backgroundColor: '#5FC9F8' }]}>
                  <MaterialIcons name="directions" size={28} color="#fff" />
                </View>
                <View style={styles.appTextContainer}>
                  <Text style={styles.appName}>Apple Maps</Text>
                  <Text style={styles.appDescription}>Navigate with Apple Maps</Text>
                </View>
                <MaterialIcons name="chevron-right" size={24} color="#999" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      ) : (
        <EnhancedWazeNavigator
          destination={{
            latitude: actualDestination.latitude,
            longitude: actualDestination.longitude
          }}
          destinationName={actualDestination.name}
          onClose={onClose}
          onArrival={handleNavigationComplete}
        />
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  destinationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f9f9f9',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  destinationTextContainer: {
    marginLeft: 15,
    flex: 1,
  },
  destinationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  destinationCoords: {
    fontSize: 14,
    color: '#666',
  },
  optionsContainer: {
    padding: 20,
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 20,
  },
  navigationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  appIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  appTextContainer: {
    flex: 1,
  },
  appName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  appDescription: {
    fontSize: 14,
    color: '#666',
  },
});

export default TransferNavigationModal;