import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Alert, Modal } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON>yline, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { NavigationService, NavigationStep } from '../services/NavigationService';
import SpeedSimulator from './SpeedSimulator';

interface TurnByTurnNavigationProps {
  visible: boolean;
  destination: { latitude: number; longitude: number };
  destinationTitle?: string;
  onClose: () => void;
  onStartNavigation?: () => void;
}

export const TurnByTurnNavigation: React.FC<TurnByTurnNavigationProps> = ({
  visible,
  destination,
  destinationTitle = "Destination",
  onClose,
  onStartNavigation,
}) => {
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [currentStep, setCurrentStep] = useState<NavigationStep | null>(null);
  const [stepIndex, setStepIndex] = useState<number>(0);
  const [isNavigating, setIsNavigating] = useState<boolean>(false);
  const [routeCoordinates, setRouteCoordinates] = useState<Array<{ latitude: number; longitude: number }>>([]);
  const [remainingDistance, setRemainingDistance] = useState<string>('');
  const [remainingTime, setRemainingTime] = useState<string>('');
  const [arrivalTime, setArrivalTime] = useState<string>('');
  
  const mapRef = useRef<MapView>(null);
  const navigationService = NavigationService.getInstance();

  // Get current location
  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required for navigation');
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const coords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      setCurrentLocation(coords);
      return coords;
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Could not get your current location');
      return null;
    }
  };

  // Decode polyline
  const decodePolyline = (encoded: string): Array<{ latitude: number; longitude: number }> => {
    const coordinates: Array<{ latitude: number; longitude: number }> = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      coordinates.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return coordinates;
  };

  // Start navigation
  const startNavigation = async () => {
    const location = await getCurrentLocation();
    if (!location) return;

    setIsNavigating(true);

    try {
      const success = await navigationService.startTurnByTurnNavigationInternal(
      location,
      destination,
      (step, index) => {
        setCurrentStep(step);
        setStepIndex(index);
        
        // Update remaining distance and time using traffic-aware data
        const currentLocation = await getCurrentLocation();
        if (currentLocation) {
          try {
            const eta = await navigationService.getTrafficAwareETA(currentLocation, destination);
            if (eta) {
              setRemainingDistance(eta.distance);
              setRemainingTime(eta.duration);
              
              // Set precise arrival time in 12-hour format
              setArrivalTime(eta.arrivalTime.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: true
              }));
            } else {
              // Fallback to basic calculation if API fails
              const state = navigationService.getCurrentState();
              if (state.currentRoute) {
                const remainingSteps = state.currentRoute.steps.slice(index);
                const totalRemainingDistance = remainingSteps.reduce((acc, step) => {
                  return acc + parseFloat(step.distance.replace(/[^\d.]/g, ''));
                }, 0);
                
                setRemainingDistance(`${totalRemainingDistance.toFixed(1)} km`);
                
                // Estimate remaining time (rough calculation)
                const avgSpeed = 30; // km/h average city speed
                const remainingTimeHours = totalRemainingDistance / avgSpeed;
                const remainingTimeMinutes = Math.ceil(remainingTimeHours * 60);
                setRemainingTime(`${remainingTimeMinutes} min`);
                
                // Calculate estimated arrival time in 12-hour format
                const now = new Date();
                now.setMinutes(now.getMinutes() + remainingTimeMinutes);
                setArrivalTime(now.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit',
                  hour12: true 
                }));
              }
            }
          } catch (error) {
            console.error('Error updating navigation info:', error);
          }
        }
      },
      () => {
        // Navigation complete
        setIsNavigating(false);
        Alert.alert('Navigation Complete', 'You have arrived at your destination!');
        onStartNavigation?.();
      }
    );

    if (success) {
      // Get route coordinates for display
      const state = navigationService.getCurrentState();
      if (state.currentRoute) {
        const coordinates = decodePolyline(state.currentRoute.polyline);
        setRouteCoordinates(coordinates);
        setRemainingDistance(state.currentRoute.totalDistance);
        setRemainingTime(state.currentRoute.totalDuration);
        
        // Set first step
        if (state.currentRoute.steps.length > 0) {
          setCurrentStep(state.currentRoute.steps[0]);
          setStepIndex(0);
        }
      }
    } else {
        setIsNavigating(false);
        Alert.alert('Error', 'Could not start navigation');
      }
    } catch (error) {
      console.error('Error in startNavigation:', error);
      setIsNavigating(false);
      Alert.alert('Navigation Error', error instanceof Error ? error.message : 'Could not start navigation');
    }
  };

  // Stop navigation
  const stopNavigation = () => {
    navigationService.stopNavigation();
    setIsNavigating(false);
    setCurrentStep(null);
    setStepIndex(0);
    setRouteCoordinates([]);
    onClose?.();
  };

  // Get maneuver icon
  const getManeuverIcon = (maneuver: string): string => {
    switch (maneuver) {
      case 'turn-left': return '↰';
      case 'turn-right': return '↱';
      case 'turn-sharp-left': return '↰';
      case 'turn-sharp-right': return '↱';
      case 'turn-slight-left': return '↰';
      case 'turn-slight-right': return '↱';
      case 'straight': return '↑';
      case 'ramp-left': return '↰';
      case 'ramp-right': return '↱';
      case 'merge': return '↑';
      case 'fork-left': return '↰';
      case 'fork-right': return '↱';
      case 'roundabout-left': return '↻';
      case 'roundabout-right': return '↺';
      default: return '↑';
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header with close button */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Turn-by-Turn Navigation</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Navigation Info Panel */}
        {isNavigating && currentStep && (
          <View style={styles.navigationPanel}>
            <View style={styles.maneuverContainer}>
              <Text style={styles.maneuverIcon}>
                {getManeuverIcon(currentStep.maneuver)}
              </Text>
              <View style={styles.instructionContainer}>
                <Text style={styles.instruction}>{currentStep.instruction}</Text>
                <Text style={styles.stepInfo}>
                  In {currentStep.distance} • Step {stepIndex + 1}
                </Text>
              </View>
            </View>
            
            <View style={styles.summaryContainer}>
              <Text style={styles.summaryText}>
                {remainingDistance} • {remainingTime} • Arrival: {arrivalTime}
              </Text>
            </View>
          </View>
        )}

        {/* Map */}
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          showsUserLocation={true}
          showsMyLocationButton={!isNavigating}
          showsTraffic={true}
          followsUserLocation={isNavigating}
          showsCompass={true}
          initialRegion={{
            latitude: currentLocation?.latitude || destination.latitude,
            longitude: currentLocation?.longitude || destination.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          {/* Destination marker */}
          <Marker
            coordinate={destination}
            title={destinationTitle}
            description="Destination"
            pinColor="red"
          />

          {/* Route polyline */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="#4285F4"
              strokeWidth={6}
            />
          )}
        </MapView>
        
        {/* Speed indicator */}
        {isNavigating && (
          <SpeedSimulator
            isActive={isNavigating}
            style={styles.speedIndicator}
            destination={destination}
          />
        )}

        {/* Control Buttons */}
        <View style={styles.controlsContainer}>
          {!isNavigating ? (
            <TouchableOpacity style={styles.startButton} onPress={startNavigation}>
              <Text style={styles.buttonText}>Start Navigation</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.stopButton} onPress={stopNavigation}>
              <Text style={styles.buttonText}>Stop Navigation</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 10,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  speedIndicator: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    zIndex: 1000,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  map: {
    flex: 1,
  },
  navigationPanel: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  maneuverContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  maneuverIcon: {
    fontSize: 32,
    marginRight: 12,
    color: '#4285F4',
    fontWeight: 'bold',
  },
  instructionContainer: {
    flex: 1,
  },
  instruction: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  stepInfo: {
    fontSize: 14,
    color: '#666',
  },
  summaryContainer: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 8,
  },
  summaryText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    zIndex: 1000,
  },
  startButton: {
    backgroundColor: '#4285F4',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  stopButton: {
    backgroundColor: '#EA4335',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TurnByTurnNavigation;