import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, StatusBar, Alert } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { MaterialIcons } from '@expo/vector-icons';
import { colors } from '../theme/colors';
import * as Location from 'expo-location';
import * as Speech from 'expo-speech';

interface NavigationStep {
  distance: string;
  instruction: string;
  roadName: string;
  maneuver: 'turn-left' | 'turn-right' | 'straight' | 'merge' | 'exit' | 'roundabout' | 'ramp';
  location: {
    latitude: number;
    longitude: number;
  };
  duration?: string;
  distanceValue?: number; // in meters
}

interface NavigationRoute {
  steps: NavigationStep[];
  totalDistance: string;
  totalDuration: string;
  polyline: string;
}

interface WazeNavigatorProps {
  destination: { latitude: number; longitude: number };
  onClose?: () => void;
  onArrival?: () => void;
}

const { width, height } = Dimensions.get('window');

export const WazeNavigator: React.FC<WazeNavigatorProps> = ({
  destination,
  onClose,
  onArrival
}) => {
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [route, setRoute] = useState<NavigationRoute | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isNavigating, setIsNavigating] = useState(false);
  const [currentSpeed, setCurrentSpeed] = useState(0);
  const [routeCoordinates, setRouteCoordinates] = useState<Array<{latitude: number, longitude: number}>>([]);
  const [mapRegion, setMapRegion] = useState({
    latitude: 14.5995, // Philippines center
    longitude: 120.9842,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);
  const mapRef = useRef<MapView>(null);

  const API_KEY = 'AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ';

  // Get directions using Google Directions API
  const getDirections = async () => {
    try {
      const apiKey = 'AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ';
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${apiKey}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.routes && data.routes.length > 0) {
        const points = data.routes[0].overview_polyline.points;
        const decodedPoints = decodePolyline(points);
        setRouteCoordinates(decodedPoints);
        
        // Calculate region to fit both points
        const minLat = Math.min(origin.latitude, destination.latitude);
        const maxLat = Math.max(origin.latitude, destination.latitude);
        const minLng = Math.min(origin.longitude, destination.longitude);
        const maxLng = Math.max(origin.longitude, destination.longitude);
        
        setMapRegion({
          latitude: (minLat + maxLat) / 2,
          longitude: (minLng + maxLng) / 2,
          latitudeDelta: Math.max((maxLat - minLat) * 1.5, 0.01),
          longitudeDelta: Math.max((maxLng - minLng) * 1.5, 0.01),
        });
      }
    } catch (error) {
      console.error('Error fetching directions:', error);
      // Fallback: direct line between points
      setRouteCoordinates([origin, destination]);
    }
  };

  // Polyline decoder
  const decodePolyline = (encoded: string) => {
    const coordinates: Array<{latitude: number, longitude: number}> = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      coordinates.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return coordinates;
  };

  const getManeuverIcon = (maneuver: string) => {
    switch (maneuver) {
      case 'turn-left':
        return 'turn-left';
      case 'turn-right':
        return 'turn-right';
      case 'straight':
        return 'straight';
      case 'merge':
        return 'merge-type';
      case 'exit':
        return 'exit-to-app';
      default:
        return 'navigation';
    }
  };

  useEffect(() => {
    getDirections();
  }, [origin, destination]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Current Road Banner */}
      <View style={styles.roadBanner}>
        <MaterialIcons name="straight" size={24} color="#fff" />
        <Text style={styles.roadBannerText}>
          Bohol{'\n'}Circumferential...
        </Text>
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={mapRegion}
          showsUserLocation={false}
          showsMyLocationButton={false}
          showsTraffic={false}
          followsUserLocation={true}
          mapType="standard"
          onRegionChangeComplete={setMapRegion}
          customMapStyle={navigationMapStyle}
        >
          {/* Route polyline */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="#5E72E4"
              strokeWidth={8}
              lineCap="round"
              lineJoin="round"
            />
          )}

          {/* Current location (blue dot) */}
          <Marker
            coordinate={origin}
            anchor={{ x: 0.5, y: 0.5 }}
          >
            <View style={styles.currentLocationDot}>
              <View style={styles.currentLocationInner} />
            </View>
          </Marker>
        </MapView>

        {/* Navigation Controls */}
        
        {/* Speed indicator (bottom left) */}
        <View style={styles.speedIndicator}>
          <Text style={styles.speedDashes}>- -</Text>
          <Text style={styles.speedUnit}>km/h</Text>
        </View>

        {/* Current step info (bottom center) */}
        <View style={styles.currentStepInfo}>
          <View style={styles.stepDot} />
          <Text style={styles.stepText}>Bohol...  {instruction.duration}</Text>
          <Text style={styles.stepSubtext}>slower</Text>
        </View>

        {/* Control buttons (right side) */}
        <View style={styles.controlButtons}>
          <TouchableOpacity style={styles.controlButton}>
            <MaterialIcons name="navigation" size={20} color="#666" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.controlButton}>
            <MaterialIcons name="search" size={20} color="#666" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.controlButton}>
            <MaterialIcons name="volume-up" size={20} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Bottom info bar */}
        <View style={styles.bottomInfoBar}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={20} color="#666" />
          </TouchableOpacity>
          
          <View style={styles.tripInfo}>
            <Text style={styles.tripDuration}>{totalDuration}</Text>
            <Text style={styles.tripDetails}>{totalDistance} • 1:20 PM</Text>
          </View>
          
          <TouchableOpacity style={styles.routeOptionsButton}>
            <MaterialIcons name="alt-route" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

// Navigation map styling - clean and minimal
const navigationMapStyle = [
  {
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#f5f5f5"
      }
    ]
  },
  {
    "elementType": "labels.icon",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#616161"
      }
    ]
  },
  {
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#f5f5f5"
      }
    ]
  },
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#eeeeee"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.business",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#ffffff"
      }
    ]
  },
  {
    "featureType": "road.arterial",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#757575"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#dadada"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#616161"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "transit",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#c9c9c9"
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#9e9e9e"
      }
    ]
  }
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  roadBanner: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    backgroundColor: '#4A7C59',
    borderRadius: 20,
    paddingVertical: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  roadBannerText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 10,
    lineHeight: 24,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  currentLocationDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  currentLocationInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#2196F3',
  },
  speedIndicator: {
    position: 'absolute',
    bottom: 140,
    left: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  speedDashes: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  speedUnit: {
    fontSize: 10,
    color: '#666',
    marginTop: -2,
  },
  currentStepInfo: {
    position: 'absolute',
    bottom: 140,
    left: '50%',
    marginLeft: -80,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2196F3',
    marginRight: 8,
  },
  stepText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  stepSubtext: {
    fontSize: 11,
    color: '#666',
    marginLeft: 4,
  },
  controlButtons: {
    position: 'absolute',
    bottom: 140,
    right: 20,
    alignItems: 'center',
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  bottomInfoBar: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
    backgroundColor: '#fff',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tripInfo: {
    flex: 1,
    alignItems: 'center',
  },
  tripDuration: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 2,
  },
  tripDetails: {
    fontSize: 12,
    color: '#666',
  },
  routeOptionsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default WazeNavigator;