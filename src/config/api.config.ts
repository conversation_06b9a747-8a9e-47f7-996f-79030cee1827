export const API_CONFIG = {
  // Base API Configuration
  BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL || 'https://laravel.nangkil.com/api',
  
  // WebSocket Configuration
  WS_BASE_URL: process.env.EXPO_PUBLIC_WS_BASE_URL || 'wss://ws.mobilebooking.com/v1',
  
  // Third-party API Keys
  GOOGLE_MAPS_API_KEY: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || '',
  
  // Request Configuration
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Cache Configuration
  CACHE_DURATION: {
    SHORT: 5 * 60 * 1000, // 5 minutes
    MEDIUM: 30 * 60 * 1000, // 30 minutes
    LONG: 24 * 60 * 60 * 1000, // 24 hours
  },
  
  // Real-time Configuration
  WEBSOCKET: {
    RECONNECT_ATTEMPTS: 5,
    RECONNECT_DELAY: 2000,
    HEARTBEAT_INTERVAL: 30000,
  },
  
  // Location Configuration
  LOCATION: {
    UPDATE_INTERVAL: 5000, // 5 seconds
    ACCURACY_THRESHOLD: 10, // 10 meters
    BACKGROUND_UPDATE_INTERVAL: 30000, // 30 seconds
  },
  
  // Feature Flags
  FEATURES: {
    OFFLINE_MODE: true,
    REAL_TIME_UPDATES: true,
    VOICE_NAVIGATION: true,
    BIOMETRIC_AUTH: false,
  },
  
  // Environment Detection
  isDevelopment: process.env.NODE_ENV === 'development' || true, // Force development mode for testing
  isProduction: process.env.NODE_ENV === 'production' && false, // Disable production mode for testing
  
  // Mock Mode Configuration (Only use real API for login)
  FORCE_MOCK_MODE: true, // Enable full mock mode for development
  DISABLE_API_CALLS: false, // Keep API calls enabled for auth
  USE_REAL_LOGIN_API: true, // Only login uses real API
  FORCE_MOCK_NON_AUTH: true, // Force all non-auth services to use mock data
  
  // Endpoints
  ENDPOINTS: {
    // Authentication
    AUTH: {
      LOGIN: '/login',
      LOGOUT: '/logout',
      REFRESH: '/auth/refresh',
      VERIFY: '/auth/verify',
    },
    
    // Driver Management
    DRIVER: {
      PROFILE: '/driver/profile',
      STATUS: '/driver/status',
      LOCATION: '/driver/location',
      PERFORMANCE: '/driver/performance',
      STATS: '/driver/stats',
      PHOTO: '/driver/photo',
      DOCUMENTS: '/driver/documents',
    },
    
    // Ride Management
    RIDES: {
      AVAILABLE: '/rides/available',
      ACCEPT: (rideId: string) => `/rides/${rideId}/accept`,
      REJECT: (rideId: string) => `/rides/${rideId}/reject`,
      STATUS: (rideId: string) => `/rides/${rideId}/status`,
      ETA: (rideId: string) => `/rides/${rideId}/eta`,
    },
    
    // Booking Management
    BOOKINGS: {
      DASHBOARD: '/bookings/dashboard',
      MANAGE: '/bookings/manage',
      HISTORY: '/bookings/history',
    },
    
    // Trip Management
    TRIPS: {
      COMPLETE: '/trips/complete',
      HISTORY: '/trips/history',
      EARNINGS: (period: string) => `/earnings/${period}`,
      ANALYTICS: (period: string) => `/earnings/analytics/${period}`,
    },
    
    // Communication
    COMMUNICATION: {
      CALL: '/communication/call',
      MESSAGE: '/communication/message',
      SUPPORT: '/support/ticket',
      EMERGENCY: '/emergency/alert',
    },
    
    // Navigation
    NAVIGATION: {
      ROUTE: '/navigation/route',
      ETA: '/navigation/eta',
    },
    
    // External Services
    EXTERNAL: {
      FLIGHTS: (flightNumber: string) => `/flights/${flightNumber}/status`,
      AIRPORTS: '/airports/locations',
      PASSENGERS: '/passengers/transfers',
    },
    
    // WebSocket Events
    WS_EVENTS: {
      RIDES_UPDATES: '/rides/updates',
      BOOKINGS_UPDATES: '/bookings/updates',
      DRIVER_LOCATION: '/driver/location',
      MESSAGES: '/communication/messages',
    },
  },
} as const;

// Type definitions for configuration
export type ApiEndpoints = typeof API_CONFIG.ENDPOINTS;
export type WebSocketEvents = typeof API_CONFIG.ENDPOINTS.WS_EVENTS;
export type CacheDuration = keyof typeof API_CONFIG.CACHE_DURATION;

// Validation helper
export const validateConfig = (): boolean => {
  const requiredEnvVars = [
    'EXPO_PUBLIC_API_BASE_URL',
    'EXPO_PUBLIC_GOOGLE_MAPS_API_KEY',
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('Missing required environment variables:', missingVars);
    return false;
  }
  
  return true;
};

export default API_CONFIG;