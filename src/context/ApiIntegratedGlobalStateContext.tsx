import React, { createContext, useContext, useReducer, ReactNode, useEffect, useCallback } from 'react';
import { Driver, Ride, Location, Trip, ApiLoadingState, ApiErrorState, ConnectionState, CacheState, SyncState } from '../types';
import { authService, rideService, driverService, tripService, webSocketService, locationService, syncService } from '../services';
import { serviceManager } from '../services';
import { mockRideGenerator } from '../services/MockRideGenerator';
import { mockData, statusFilters } from '../data/mockData';
import { API_CONFIG } from '../config/api.config';

interface GlobalState {
  // Auth state
  isAuthenticated: boolean;
  driver: Driver | null;
  token: string | null;
  
  // App state
  currentRide: Ride | null;
  location: Location | null;
  trips: Trip[];
  totalEarnings: number;
  pendingBookings: Ride[];
  weeklyEarnings: number;
  todayTrips: number;
  availableDrivers: Driver[];
  sidebarOpen: boolean;
  navigationVisible: boolean;
  isOnline: boolean;
  queueStatus: 'available' | 'limited_queue' | 'service_mode' | 'completing';
  queuedBookings: Ride[];
  maxQueueSize: number;
  rideStatus: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null;
  availableRides: Ride[];
  mockDataGenerated: boolean;
  mapRegion: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  
  // API State Management
  loading: ApiLoadingState;
  errors: ApiErrorState;
  connection: ConnectionState;
  cache: CacheState;
  sync: SyncState;
}

type GlobalAction = 
  // Auth actions
  | { type: 'LOGIN'; payload: { driver: Driver; token: string } }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_DRIVER'; payload: Driver }
  // App actions
  | { type: 'SET_LOCATION'; payload: Location }
  | { type: 'SET_CURRENT_RIDE'; payload: Ride | null }
  | { type: 'ADD_TRIP'; payload: Trip }
  | { type: 'UPDATE_EARNINGS'; payload: number }
  | { type: 'SET_AVAILABLE_DRIVERS'; payload: Driver[] }
  | { type: 'TOGGLE_SIDEBAR'; payload?: boolean }
  | { type: 'SET_MAP_REGION'; payload: { latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number } }
  | { type: 'ADD_PENDING_BOOKING'; payload: Ride }
  | { type: 'REMOVE_PENDING_BOOKING'; payload: string }
  | { type: 'SET_NAVIGATION_VISIBILITY'; payload: boolean }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'SET_QUEUE_STATUS'; payload: 'available' | 'limited_queue' | 'service_mode' | 'completing' }
  | { type: 'ADD_QUEUED_BOOKING'; payload: Ride }
  | { type: 'REMOVE_QUEUED_BOOKING'; payload: string }
  | { type: 'SET_QUEUED_BOOKINGS'; payload: Ride[] }
  | { type: 'SET_MAX_QUEUE_SIZE'; payload: number }
  | { type: 'SET_RIDE_STATUS'; payload: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null }
  | { type: 'SET_AVAILABLE_RIDES'; payload: Ride[] }
  | { type: 'ADD_AVAILABLE_RIDE'; payload: Ride }
  | { type: 'REMOVE_AVAILABLE_RIDE'; payload: string }
  | { type: 'SET_MOCK_DATA_GENERATED'; payload: boolean }
  // API State Management actions
  | { type: 'SET_LOADING'; payload: { section: keyof ApiLoadingState; loading: boolean } }
  | { type: 'SET_ERROR'; payload: { section: keyof ApiErrorState; error: string | null } }
  | { type: 'SET_CONNECTION_STATE'; payload: Partial<ConnectionState> }
  | { type: 'SET_CACHE'; payload: { section: keyof CacheState; data: any } }
  | { type: 'CLEAR_CACHE'; payload?: keyof CacheState }
  | { type: 'ADD_PENDING_ACTION'; payload: { type: string; payload: any } }
  | { type: 'REMOVE_PENDING_ACTION'; payload: string }
  | { type: 'SET_SYNC_STATUS'; payload: { isSyncing: boolean; lastSyncTime?: Date } }
  // New API integrated actions
  | { type: 'SET_TRIPS'; payload: Trip[] }
  | { type: 'SET_WEEKLY_EARNINGS'; payload: number }
  | { type: 'SET_TODAY_TRIPS'; payload: number };

interface GlobalContextType {
  state: GlobalState;
  // Auth methods
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateDriver: (driver: Driver) => Promise<void>;
  // App methods
  setLocation: (location: Location) => void;
  setCurrentRide: (ride: Ride | null) => void;
  addTrip: (trip: Trip) => void;
  updateEarnings: (earnings: number) => void;
  setAvailableDrivers: (drivers: Driver[]) => void;
  toggleSidebar: (open?: boolean) => void;
  setMapRegion: (region: { latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number }) => void;
  addPendingBooking: (booking: Ride) => void;
  removePendingBooking: (bookingId: string) => void;
  setNavigationVisibility: (visible: boolean) => void;
  setOnlineStatus: (isOnline: boolean) => Promise<void>;
  setQueueStatus: (status: 'available' | 'limited_queue' | 'service_mode' | 'completing') => void;
  addQueuedBooking: (booking: Ride) => void;
  removeQueuedBooking: (bookingId: string) => void;
  setQueuedBookings: (bookings: Ride[]) => void;
  setRideStatus: (status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null) => void;
  shouldShowBookings: () => boolean;
  canAcceptNewBooking: () => boolean;
  getMaxQueueSize: () => number;
  startNextQueuedRide: () => Ride | null;
  setAvailableRides: (rides: Ride[]) => void;
  removeAvailableRide: (rideId: string) => void;
  generateMockRides: () => void;
  loadAvailableRides: () => Promise<void>;
  // API-integrated booking management
  getAllBookings: () => Ride[];
  getBookingsByStatus: (status: 'pending' | 'accepted' | 'ongoing' | 'completed' | 'cancelled' | 'rejected') => Ride[];
  getBookingsByType: (bookingStatus: 'new' | 'urgent' | 'scheduled' | 'popular' | 'premium') => Ride[];
  getActiveBookings: () => Ride[];
  getScheduledBookings: () => Ride[];
  acceptBooking: (rideId: string) => Promise<boolean>;
  rejectBooking: (rideId: string) => Promise<void>;
  completeBooking: (rideId: string) => Promise<void>;
  // API State Management methods
  setLoading: (section: keyof ApiLoadingState, loading: boolean) => void;
  setError: (section: keyof ApiErrorState, error: string | null) => void;
  clearErrors: () => void;
  setConnectionState: (state: Partial<ConnectionState>) => void;
  isDataStale: (section: keyof CacheState, maxAge?: number) => boolean;
  addPendingAction: (actionType: string, payload: any) => void;
  clearPendingActions: () => void;
  setSyncStatus: (isSyncing: boolean, lastSyncTime?: Date) => void;
  // Data loading methods
  loadTripHistory: () => Promise<void>;
  loadEarningsData: () => Promise<void>;
  loadDriverProfile: () => Promise<void>;
  refreshAllData: () => Promise<void>;
}

const initialState: GlobalState = {
  // Auth state
  isAuthenticated: false,
  driver: null,
  token: null,
  
  // App state
  currentRide: null,
  location: null,
  trips: [],
  totalEarnings: 0,
  pendingBookings: [],
  weeklyEarnings: 0,
  todayTrips: 0,
  availableDrivers: [],
  sidebarOpen: false,
  navigationVisible: true,
  isOnline: false,
  queueStatus: 'available',
  queuedBookings: [],
  maxQueueSize: 3,
  rideStatus: null,
  availableRides: [],
  mockDataGenerated: false,
  mapRegion: {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  
  // API State Management
  loading: {
    auth: false,
    rides: false,
    trips: false,
    driver: false,
    earnings: false,
    bookings: false,
    navigation: false,
    communication: false,
  },
  errors: {
    auth: null,
    rides: null,
    trips: null,
    driver: null,
    earnings: null,
    bookings: null,
    navigation: null,
    communication: null,
    network: null,
  },
  connection: {
    isOnline: true,
    isApiConnected: false,
    isWebSocketConnected: false,
    lastConnectionCheck: null,
    retryCount: 0,
  },
  cache: {
    rides: null,
    trips: null,
    earnings: null,
    driver: null,
  },
  sync: {
    pendingActions: [],
    lastSyncTime: null,
    isSyncing: false,
  },
};

const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

const globalReducer = (state: GlobalState, action: GlobalAction): GlobalState => {
  switch (action.type) {
    // Auth actions
    case 'LOGIN':
      return {
        ...state,
        isAuthenticated: true,
        driver: action.payload.driver,
        token: action.payload.token,
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isAuthenticated: false,
        driver: null,
        token: null,
      };
    case 'UPDATE_DRIVER':
      return {
        ...state,
        driver: action.payload,
      };
      
    // App actions
    case 'SET_LOCATION':
      return {
        ...state,
        location: action.payload,
      };
    case 'SET_CURRENT_RIDE':
      return {
        ...state,
        currentRide: action.payload,
      };
    case 'ADD_TRIP':
      return {
        ...state,
        trips: [action.payload, ...state.trips],
        totalEarnings: state.totalEarnings + action.payload.earnings,
      };
    case 'SET_TRIPS':
      const totalEarnings = action.payload.reduce((sum, trip) => sum + trip.earnings, 0);
      return {
        ...state,
        trips: action.payload,
        totalEarnings,
      };
    case 'UPDATE_EARNINGS':
      return {
        ...state,
        totalEarnings: action.payload,
      };
    case 'SET_WEEKLY_EARNINGS':
      return {
        ...state,
        weeklyEarnings: action.payload,
      };
    case 'SET_TODAY_TRIPS':
      return {
        ...state,
        todayTrips: action.payload,
      };
    case 'SET_AVAILABLE_DRIVERS':
      return {
        ...state,
        availableDrivers: action.payload,
      };
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        sidebarOpen: action.payload !== undefined ? action.payload : !state.sidebarOpen,
      };
    case 'SET_MAP_REGION':
      return {
        ...state,
        mapRegion: action.payload,
      };
    case 'ADD_PENDING_BOOKING':
      return {
        ...state,
        pendingBookings: [...state.pendingBookings, action.payload],
      };
    case 'REMOVE_PENDING_BOOKING':
      return {
        ...state,
        pendingBookings: state.pendingBookings.filter(booking => booking.id !== action.payload),
      };
    case 'SET_NAVIGATION_VISIBILITY':
      return {
        ...state,
        navigationVisible: action.payload,
      };
    case 'SET_ONLINE_STATUS':
      return {
        ...state,
        isOnline: action.payload,
      };
    case 'SET_QUEUE_STATUS':
      return {
        ...state,
        queueStatus: action.payload,
      };
    case 'ADD_QUEUED_BOOKING':
      return {
        ...state,
        queuedBookings: [...state.queuedBookings, action.payload],
      };
    case 'REMOVE_QUEUED_BOOKING':
      return {
        ...state,
        queuedBookings: state.queuedBookings.filter(booking => booking.id !== action.payload),
      };
    case 'SET_QUEUED_BOOKINGS':
      return {
        ...state,
        queuedBookings: action.payload,
      };
    case 'SET_MAX_QUEUE_SIZE':
      return {
        ...state,
        maxQueueSize: action.payload,
      };
    case 'SET_RIDE_STATUS':
      return {
        ...state,
        rideStatus: action.payload,
      };
    case 'SET_AVAILABLE_RIDES':
      return {
        ...state,
        availableRides: action.payload,
      };
    case 'ADD_AVAILABLE_RIDE':
      return {
        ...state,
        availableRides: [...state.availableRides, action.payload],
      };
    case 'REMOVE_AVAILABLE_RIDE':
      return {
        ...state,
        availableRides: state.availableRides.filter(ride => ride.id !== action.payload),
      };
    case 'SET_MOCK_DATA_GENERATED':
      return {
        ...state,
        mockDataGenerated: action.payload,
      };
    
    // API State Management actions
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.section]: action.payload.loading,
        },
      };
    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.section]: action.payload.error,
        },
      };
    case 'SET_CONNECTION_STATE':
      return {
        ...state,
        connection: {
          ...state.connection,
          ...action.payload,
        },
      };
    case 'SET_CACHE':
      return {
        ...state,
        cache: {
          ...state.cache,
          [action.payload.section]: {
            data: action.payload.data,
            timestamp: Date.now(),
          },
        },
      };
    case 'CLEAR_CACHE':
      if (action.payload) {
        return {
          ...state,
          cache: {
            ...state.cache,
            [action.payload]: null,
          },
        };
      } else {
        return {
          ...state,
          cache: {
            rides: null,
            trips: null,
            earnings: null,
            driver: null,
          },
        };
      }
    case 'ADD_PENDING_ACTION':
      return {
        ...state,
        sync: {
          ...state.sync,
          pendingActions: [
            ...state.sync.pendingActions,
            {
              id: `action-${Date.now()}-${Math.random()}`,
              type: action.payload.type,
              payload: action.payload.payload,
              timestamp: new Date(),
              retryCount: 0,
            },
          ],
        },
      };
    case 'REMOVE_PENDING_ACTION':
      return {
        ...state,
        sync: {
          ...state.sync,
          pendingActions: state.sync.pendingActions.filter(
            action => action.id !== action.payload
          ),
        },
      };
    case 'SET_SYNC_STATUS':
      return {
        ...state,
        sync: {
          ...state.sync,
          isSyncing: action.payload.isSyncing,
          lastSyncTime: action.payload.lastSyncTime || state.sync.lastSyncTime,
        },
      };
    default:
      return state;
  }
};

export const GlobalStateProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(globalReducer, initialState);

  // Initialize services and set up listeners
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Ensure services are initialized
        if (!serviceManager.isInitialized()) {
          await serviceManager.initialize();
        }

        // Check authentication status
        const authStatus = await authService.getAuthStatus();
        if (authStatus.isAuthenticated && authStatus.driver) {
          dispatch({
            type: 'LOGIN',
            payload: {
              driver: authStatus.driver,
              token: 'retrieved-from-storage',
            },
          });

          // Load initial data for authenticated user
          await loadInitialData(authStatus.driver.id);

          // Generate mock data immediately if in mock mode
          if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
            console.log('🎭 Mock mode: Generating initial mock data after authentication');
            
            // Set driver online automatically in mock mode for better UX
            dispatch({ type: 'SET_ONLINE_STATUS', payload: true });
            
            generateMockRides();
            
            // Start generating new rides periodically in mock mode
            mockRideGenerator.startGeneratingRides();
            
            // Generate mock trips for earnings display
            const mockTrips = mockData.generateTrips();
            dispatch({ type: 'SET_TRIPS', payload: mockTrips });
          }
        }

        // Initialize location services
        const hasLocationPermission = await locationService.requestPermissions();
        if (hasLocationPermission) {
          const currentLocation = await locationService.getCurrentLocation();
          if (currentLocation) {
            const address = await locationService.reverseGeocode(
              currentLocation.coords.latitude,
              currentLocation.coords.longitude
            );
            
            dispatch({
              type: 'SET_LOCATION',
              payload: {
                latitude: currentLocation.coords.latitude,
                longitude: currentLocation.coords.longitude,
                address: address || `${currentLocation.coords.latitude}, ${currentLocation.coords.longitude}`,
              },
            });
          }
        }

        // Set up WebSocket connection if authenticated
        if (authStatus.isAuthenticated && authStatus.driver) {
          await webSocketService.connect(authStatus.driver.id);
        }

      } catch (error) {
        console.error('Failed to initialize app:', error);
        setError('network', 'Failed to initialize application');
      }
    };

    initializeApp();

    // Cleanup on unmount
    return () => {
      webSocketService.disconnect();
    };
  }, []);

  // Subscribe to mock ride requests
  useEffect(() => {
    if (!API_CONFIG.FORCE_MOCK_MODE && !API_CONFIG.DISABLE_API_CALLS) {
      return; // Only in mock mode
    }

    const unsubscribe = mockRideGenerator.onNewRide((ride) => {
      if (state.driver) {
        console.log('📥 New mock ride request received:', ride.passengerName);
        dispatch({ type: 'ADD_AVAILABLE_RIDE', payload: ride });
        
        // Show notification or alert for new ride if driver is online
        if (state.isOnline) {
          console.log('🔔 Driver is online - ride available for acceptance');
        }
      }
    });

    return unsubscribe;
  }, [state.driver]); // Removed isOnline dependency

  // Load initial data for authenticated driver
  const loadInitialData = useCallback(async (driverId: string) => {
    try {
      await Promise.allSettled([
        loadTripHistory(),
        loadEarningsData(),
        loadDriverProfile(),
        loadAvailableRides(),
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  }, []);

  // Auth methods
  const login = async (email: string, password: string): Promise<void> => {
    try {
      setLoading('auth', true);
      setError('auth', null);

      const response = await authService.login({ email, password });
      
      dispatch({
        type: 'LOGIN',
        payload: {
          driver: response.driver,
          token: response.token,
        },
      });

      // Connect to WebSocket
      await webSocketService.connect(response.driver.id);

      // Load initial data
      await loadInitialData(response.driver.id);

      // Generate mock data immediately if in mock mode
      if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
        console.log('🎭 Mock mode: Generating initial mock data after login');
        
        // Set driver online automatically in mock mode for better UX
        dispatch({ type: 'SET_ONLINE_STATUS', payload: true });
        
        generateMockRides();
        
        // Start generating new rides periodically in mock mode
        mockRideGenerator.startGeneratingRides();
        
        // Generate mock trips for earnings display
        const mockTrips = mockData.generateTrips();
        dispatch({ type: 'SET_TRIPS', payload: mockTrips });
      }

    } catch (error: any) {
      setError('auth', error.message);
      throw error;
    } finally {
      setLoading('auth', false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setLoading('auth', true);
      
      await authService.logout();
      webSocketService.disconnect();
      
      dispatch({ type: 'LOGOUT' });
      
    } catch (error: any) {
      console.error('Logout error:', error);
      // Continue with logout even if server request fails
      dispatch({ type: 'LOGOUT' });
    } finally {
      setLoading('auth', false);
    }
  };

  const updateDriver = async (driver: Driver): Promise<void> => {
    try {
      setLoading('driver', true);
      setError('driver', null);

      const updatedDriver = await driverService.updateProfile(driver.id, {
        name: driver.name,
        phone: driver.phone,
        email: driver.email,
        vehicleType: driver.vehicleType,
        licensePlate: driver.licensePlate,
      });

      dispatch({ type: 'UPDATE_DRIVER', payload: updatedDriver });

    } catch (error: any) {
      setError('driver', error.message);
      throw error;
    } finally {
      setLoading('driver', false);
    }
  };

  // Data loading methods
  const loadTripHistory = async (): Promise<void> => {
    if (!state.driver) return;

    try {
      setLoading('trips', true);
      setError('trips', null);

      const response = await tripService.getTripHistory({
        driverId: state.driver.id,
        limit: 50,
        sortBy: 'date',
        sortOrder: 'desc',
      });

      dispatch({ type: 'SET_TRIPS', payload: response.trips.map(trip => ({
        id: trip.id,
        rideId: trip.rideId,
        earnings: trip.earnings,
        distance: trip.distance,
        duration: trip.duration,
        completedAt: trip.completedAt,
      })) });

    } catch (error: any) {
      setError('trips', error.message);
      console.error('Error loading trip history:', error);
    } finally {
      setLoading('trips', false);
    }
  };

  const loadEarningsData = async (): Promise<void> => {
    if (!state.driver) return;

    try {
      setLoading('earnings', true);
      setError('earnings', null);

      const [weeklyEarnings, todayStats] = await Promise.all([
        tripService.getEarnings({
          driverId: state.driver.id,
          period: 'week',
        }),
        tripService.getEarnings({
          driverId: state.driver.id,
          period: 'today',
        }),
      ]);

      dispatch({ type: 'SET_WEEKLY_EARNINGS', payload: weeklyEarnings.totalEarnings });
      dispatch({ type: 'SET_TODAY_TRIPS', payload: todayStats.tripCount });

    } catch (error: any) {
      setError('earnings', error.message);
      console.error('Error loading earnings data:', error);
    } finally {
      setLoading('earnings', false);
    }
  };

  const loadDriverProfile = async (): Promise<void> => {
    if (!state.driver) return;

    try {
      setLoading('driver', true);
      setError('driver', null);

      const driver = await driverService.getProfile(state.driver.id);
      dispatch({ type: 'UPDATE_DRIVER', payload: driver });

    } catch (error: any) {
      setError('driver', error.message);
      console.error('Error loading driver profile:', error);
    } finally {
      setLoading('driver', false);
    }
  };

  const loadAvailableRides = useCallback(async (): Promise<void> => {
    if (!state.driver) return;

    try {
      setLoading('rides', true);
      setError('rides', null);

      // Handle mock mode
      if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
        console.log('🎭 Mock mode: Loading mock rides');
        const mockRides = mockData.generateAvailableRides();
        dispatch({ type: 'SET_AVAILABLE_RIDES', payload: mockRides });
        return;
      }

      // Regular API call for production (requires location)
      if (!state.location) {
        console.warn('Cannot load rides without location');
        return;
      }

      const response = await rideService.getAvailableRides({
        driverId: state.driver.id,
        latitude: state.location.latitude,
        longitude: state.location.longitude,
        limit: 20,
      });

      dispatch({ type: 'SET_AVAILABLE_RIDES', payload: response.rides });

    } catch (error: any) {
      setError('rides', error.message);
      console.error('Error loading available rides:', error);
    } finally {
      setLoading('rides', false);
    }
  }, [state.driver, state.location]);

  const refreshAllData = async (): Promise<void> => {
    if (!state.driver) return;

    await Promise.allSettled([
      loadTripHistory(),
      loadEarningsData(),
      loadDriverProfile(),
      loadAvailableRides(),
    ]);
  };

  // App methods
  const setLocation = (location: Location) => {
    dispatch({ type: 'SET_LOCATION', payload: location });
    
    // Update driver location via API if online and authenticated
    if (state.isOnline && state.driver) {
      driverService.updateLocation(state.driver.id, { location }).catch(error => {
        console.warn('Failed to update driver location:', error);
      });
    }
  };

  const setCurrentRide = (ride: Ride | null) => {
    dispatch({ type: 'SET_CURRENT_RIDE', payload: ride });
  };

  const addTrip = (trip: Trip) => {
    dispatch({ type: 'ADD_TRIP', payload: trip });
  };

  const updateEarnings = (earnings: number) => {
    dispatch({ type: 'UPDATE_EARNINGS', payload: earnings });
  };

  const setAvailableDrivers = (drivers: Driver[]) => {
    dispatch({ type: 'SET_AVAILABLE_DRIVERS', payload: drivers });
  };

  const toggleSidebar = (open?: boolean) => {
    dispatch({ type: 'TOGGLE_SIDEBAR', payload: open });
  };

  const setMapRegion = (region: { latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number }) => {
    dispatch({ type: 'SET_MAP_REGION', payload: region });
  };

  const addPendingBooking = (booking: Ride) => {
    dispatch({ type: 'ADD_PENDING_BOOKING', payload: booking });
  };

  const removePendingBooking = (bookingId: string) => {
    dispatch({ type: 'REMOVE_PENDING_BOOKING', payload: bookingId });
  };

  const setNavigationVisibility = (visible: boolean) => {
    dispatch({ type: 'SET_NAVIGATION_VISIBILITY', payload: visible });
  };

  const setOnlineStatus = async (isOnline: boolean): Promise<void> => {
    if (!state.driver) return;

    try {
      dispatch({ type: 'SET_ONLINE_STATUS', payload: isOnline });
      
      // Handle mock mode
      if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
        if (isOnline) {
          console.log('🎭 Driver went online - ensuring mock rides are available');
          
          // Generate initial rides if none exist
          if (state.availableRides.length === 0) {
            console.log('🎭 No rides available, generating initial mock rides');
            generateMockRides();
          }
          
          // Start generating new rides periodically
          mockRideGenerator.startGeneratingRides();
        } else {
          console.log('🎭 Driver went offline - stopping mock ride requests');
          mockRideGenerator.stopGeneratingRides();
        }
        return; // Skip API call in mock mode
      }

      // Regular API call for production
      await driverService.updateStatus(state.driver.id, {
        isOnline,
        location: state.location || undefined,
      });

    } catch (error: any) {
      // Revert on error
      dispatch({ type: 'SET_ONLINE_STATUS', payload: !isOnline });
      setError('driver', error.message);
      throw error;
    }
  };

  const setQueueStatus = (status: 'available' | 'limited_queue' | 'service_mode' | 'completing') => {
    dispatch({ type: 'SET_QUEUE_STATUS', payload: status });
  };

  const addQueuedBooking = (booking: Ride) => {
    dispatch({ type: 'ADD_QUEUED_BOOKING', payload: booking });
  };

  const removeQueuedBooking = (bookingId: string) => {
    dispatch({ type: 'REMOVE_QUEUED_BOOKING', payload: bookingId });
  };

  const setQueuedBookings = (bookings: Ride[]) => {
    dispatch({ type: 'SET_QUEUED_BOOKINGS', payload: bookings });
  };

  const setRideStatus = (status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null) => {
    dispatch({ type: 'SET_RIDE_STATUS', payload: status });
    const newMaxQueueSize = getMaxQueueSizeForStatus(status);
    dispatch({ type: 'SET_MAX_QUEUE_SIZE', payload: newMaxQueueSize });
  };

  const getMaxQueueSizeForStatus = (status: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null): number => {
    if (!status) return 3;
    switch (status) {
      case 'heading_to_pickup': return 3;
      case 'arrived_at_pickup': return 2;
      case 'passenger_onboard': return 0;
      case 'arrived_at_destination': return 1;
      default: return 3;
    }
  };

  const shouldShowBookings = (): boolean => {
    if (!state.isOnline) return false;
    if (!state.currentRide) return true;
    
    if (state.rideStatus === 'passenger_onboard' || state.rideStatus === 'arrived_at_destination') {
      return false;
    }
    
    return true;
  };

  const canAcceptNewBooking = (): boolean => {
    if (!state.isOnline) return false;
    if (!state.currentRide) return true;
    
    if (state.rideStatus === 'passenger_onboard' || state.rideStatus === 'arrived_at_destination') {
      return false;
    }
    
    return state.queuedBookings.length < state.maxQueueSize;
  };

  const getMaxQueueSize = (): number => {
    return state.maxQueueSize;
  };

  const startNextQueuedRide = () => {
    if (state.queuedBookings.length > 0) {
      const nextRide = state.queuedBookings[0];
      dispatch({ type: 'SET_CURRENT_RIDE', payload: nextRide });
      dispatch({ type: 'REMOVE_QUEUED_BOOKING', payload: nextRide.id });
      dispatch({ type: 'SET_RIDE_STATUS', payload: 'heading_to_pickup' });
      dispatch({ type: 'SET_QUEUE_STATUS', payload: 'limited_queue' });
      return nextRide;
    } else {
      dispatch({ type: 'SET_QUEUE_STATUS', payload: 'available' });
      return null;
    }
  };

  const setAvailableRides = (rides: Ride[]) => {
    dispatch({ type: 'SET_AVAILABLE_RIDES', payload: rides });
  };

  const removeAvailableRide = (rideId: string) => {
    dispatch({ type: 'REMOVE_AVAILABLE_RIDE', payload: rideId });
  };

  const generateMockRides = () => {
    if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Generating mock rides');
      const mockRides = mockData.generateAvailableRides();
      dispatch({ type: 'SET_AVAILABLE_RIDES', payload: mockRides });
      dispatch({ type: 'SET_MOCK_DATA_GENERATED', payload: true });
    } else {
      console.log('Not in mock mode, skipping mock ride generation');
    }
  };

  // Unified booking management methods
  const getAllBookings = (): Ride[] => {
    return [
      ...state.availableRides,
      ...state.queuedBookings,
      ...(state.currentRide ? [state.currentRide] : []),
      ...state.pendingBookings
    ];
  };

  const getBookingsByStatus = (status: 'pending' | 'accepted' | 'ongoing' | 'completed' | 'cancelled' | 'rejected'): Ride[] => {
    return statusFilters.filterByLifecycleStatus(getAllBookings(), status);
  };

  const getBookingsByType = (bookingStatus: 'new' | 'urgent' | 'scheduled' | 'popular' | 'premium'): Ride[] => {
    return statusFilters.filterByBookingStatus(getAllBookings(), bookingStatus);
  };

  const getActiveBookings = (): Ride[] => {
    // Get urgent bookings using the standardized filter
    return statusFilters.getUrgentBookings(getAllBookings());
  };

  const getScheduledBookings = (): Ride[] => {
    // Get scheduled bookings with time constraint
    return statusFilters.getScheduledBookings(getAllBookings()).filter(ride => ride.scheduledTime);
  };

  const acceptBooking = async (rideId: string): Promise<boolean> => {
    if (!state.driver) return false;

    try {
      setLoading('rides', true);
      setError('rides', null);

      // Handle mock mode
      if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
        console.log('🎭 Mock mode: Accepting booking', rideId);
        
        // Find the ride to accept
        const ride = state.availableRides.find(r => r.id === rideId);
        if (!ride) {
          console.error('❌ Ride not found:', rideId);
          return false;
        }
        
        // Update ride status to accepted
        const acceptedRide: Ride = {
          ...ride,
          status: 'accepted',
        };
        
        // If driver has current ride, queue this one
        if (state.currentRide) {
          console.log('🚗 Driver busy, adding to queue:', ride.passengerName);
          addQueuedBooking(acceptedRide);
        } else {
          console.log('🚗 Setting as current ride:', ride.passengerName);
          setCurrentRide(acceptedRide);
          setRideStatus('heading_to_pickup');
          setQueueStatus('limited_queue');
        }
        
        // Remove from available rides
        removeAvailableRide(rideId);
        console.log('✅ Mock booking accepted successfully');
        return true;
      }

      // Production API call
      if (!state.location) {
        console.error('❌ Location required for production mode');
        return false;
      }

      const response = await rideService.acceptRide({
        rideId,
        driverId: state.driver.id,
        currentLocation: state.location,
      });

      if (response.success) {
        const ride = response.ride;
        
        if (state.currentRide) {
          addQueuedBooking(ride);
        } else {
          setCurrentRide(ride);
          setRideStatus('heading_to_pickup');
          setQueueStatus('limited_queue');
        }
        
        removeAvailableRide(rideId);
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('❌ Error accepting booking:', error);
      setError('rides', error.message);
      return false;
    } finally {
      setLoading('rides', false);
    }
  };

  const rejectBooking = async (rideId: string): Promise<void> => {
    if (!state.driver) return;

    try {
      setLoading('rides', true);
      setError('rides', null);

      // Handle mock mode
      if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
        console.log('🎭 Mock mode: Rejecting booking', rideId);
        const ride = state.availableRides.find(r => r.id === rideId);
        if (ride) {
          console.log('❌ Rejected ride:', ride.passengerName);
        }
        removeAvailableRide(rideId);
        return;
      }

      // Production API call
      await rideService.rejectRide({
        rideId,
        driverId: state.driver.id,
        reason: 'other',
      });

      removeAvailableRide(rideId);
    } catch (error: any) {
      console.error('❌ Error rejecting booking:', error);
      setError('rides', error.message);
      throw error;
    } finally {
      setLoading('rides', false);
    }
  };

  const completeBooking = async (rideId: string): Promise<void> => {
    if (!state.driver) return;

    try {
      setLoading('trips', true);
      setError('trips', null);

      // Handle mock mode
      if (API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
        console.log('🎭 Mock mode: Completing booking', rideId);
        
        // Find the ride (could be current ride or queued)
        const ride = state.currentRide?.id === rideId 
          ? state.currentRide 
          : state.queuedBookings.find(r => r.id === rideId);
          
        if (!ride) {
          console.error('❌ Ride not found for completion:', rideId);
          return;
        }
        
        // Generate mock trip data
        const trip: Trip = {
          id: `trip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          rideId: rideId,
          earnings: ride.fare || Math.round((15 + Math.random() * 35) * 100) / 100,
          distance: ride.distance || Math.round((3 + Math.random() * 15) * 10) / 10,
          duration: ride.duration || Math.round(20 + Math.random() * 40),
          completedAt: new Date(),
        };

        console.log('✅ Mock trip completed:', ride.passengerName, `$${trip.earnings}`);
        addTrip(trip);
        
        if (state.currentRide?.id === rideId) {
          setCurrentRide(null);
          setRideStatus(null);
          startNextQueuedRide();
        } else {
          removeQueuedBooking(rideId);
        }
        
        return;
      }

      // Production API call
      if (!state.location) {
        console.error('❌ Location required for production mode');
        return;
      }

      const completedTrip = await tripService.completeRide({
        rideId,
        driverId: state.driver.id,
        endLocation: state.location,
      });

      // Add to trips and update earnings
      const trip: Trip = {
        id: completedTrip.id,
        rideId: completedTrip.rideId,
        earnings: completedTrip.earnings,
        distance: completedTrip.distance,
        duration: completedTrip.duration,
        completedAt: completedTrip.completedAt,
      };

      addTrip(trip);
      
      if (state.currentRide?.id === rideId) {
        setCurrentRide(null);
        setRideStatus(null);
        startNextQueuedRide();
      } else {
        removeQueuedBooking(rideId);
      }

      // Refresh earnings data (skip in mock mode)
      await loadEarningsData();

    } catch (error: any) {
      setError('trips', error.message);
      throw error;
    } finally {
      setLoading('trips', false);
    }
  };
  
  // API State Management methods
  const setLoading = (section: keyof ApiLoadingState, loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: { section, loading } });
  };

  const setError = (section: keyof ApiErrorState, error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: { section, error } });
  };

  const clearErrors = () => {
    const errorKeys: (keyof ApiErrorState)[] = [
      'auth', 'rides', 'trips', 'driver', 'earnings', 'bookings', 'navigation', 'communication', 'network'
    ];
    errorKeys.forEach(key => {
      dispatch({ type: 'SET_ERROR', payload: { section: key, error: null } });
    });
  };

  const setConnectionState = (connectionState: Partial<ConnectionState>) => {
    dispatch({ type: 'SET_CONNECTION_STATE', payload: connectionState });
  };

  const isDataStale = (section: keyof CacheState, maxAge: number = 5 * 60 * 1000): boolean => {
    const cached = state.cache[section];
    if (!cached) return true;
    
    const age = Date.now() - cached.timestamp;
    return age > maxAge;
  };

  const addPendingAction = (actionType: string, payload: any) => {
    dispatch({ type: 'ADD_PENDING_ACTION', payload: { type: actionType, payload } });
  };

  const clearPendingActions = () => {
    state.sync.pendingActions.forEach(action => {
      dispatch({ type: 'REMOVE_PENDING_ACTION', payload: action.id });
    });
  };

  const setSyncStatus = (isSyncing: boolean, lastSyncTime?: Date) => {
    dispatch({ type: 'SET_SYNC_STATUS', payload: { isSyncing, lastSyncTime } });
  };

  return (
    <GlobalContext.Provider value={{ 
      state, 
      login,
      logout,
      updateDriver,
      setLocation, 
      setCurrentRide, 
      addTrip, 
      updateEarnings,
      setAvailableDrivers,
      toggleSidebar,
      setMapRegion,
      addPendingBooking,
      removePendingBooking,
      setNavigationVisibility,
      setOnlineStatus,
      setQueueStatus,
      addQueuedBooking,
      removeQueuedBooking,
      setQueuedBookings,
      setRideStatus,
      shouldShowBookings,
      canAcceptNewBooking,
      getMaxQueueSize,
      startNextQueuedRide,
      setAvailableRides,
      removeAvailableRide,
      generateMockRides,
      loadAvailableRides,
      // Unified booking management
      getAllBookings,
      getBookingsByStatus,
      getBookingsByType,
      getActiveBookings,
      getScheduledBookings,
      acceptBooking,
      rejectBooking,
      completeBooking,
      // API State Management methods
      setLoading,
      setError,
      clearErrors,
      setConnectionState,
      isDataStale,
      addPendingAction,
      clearPendingActions,
      setSyncStatus,
      // Data loading methods
      loadTripHistory,
      loadEarningsData,
      loadDriverProfile,
      refreshAllData,
    }}>
      {children}
    </GlobalContext.Provider>
  );
};

export const useGlobalState = () => {
  const context = useContext(GlobalContext);
  if (context === undefined) {
    throw new Error('useGlobalState must be used within a GlobalStateProvider');
  }
  return context;
};

export const useAuth = () => {
  const { state, login, logout, updateDriver, setLoading, setError } = useGlobalState();
  return {
    state: {
      isAuthenticated: state.isAuthenticated,
      driver: state.driver,
      token: state.token,
      loading: state.loading.auth,
      error: state.errors.auth,
    },
    login,
    logout,
    updateDriver,
    setLoading: (loading: boolean) => setLoading('auth', loading),
    setError: (error: string | null) => setError('auth', error),
  };
};

export const useApp = () => {
  const { state, ...methods } = useGlobalState();
  return {
    state: {
      currentRide: state.currentRide,
      location: state.location,
      trips: state.trips,
      totalEarnings: state.totalEarnings,
      pendingBookings: state.pendingBookings,
      weeklyEarnings: state.weeklyEarnings,
      todayTrips: state.todayTrips,
      availableDrivers: state.availableDrivers,
      sidebarOpen: state.sidebarOpen,
      navigationVisible: state.navigationVisible,
      isOnline: state.isOnline,
      queueStatus: state.queueStatus,
      queuedBookings: state.queuedBookings,
      maxQueueSize: state.maxQueueSize,
      rideStatus: state.rideStatus,
      availableRides: state.availableRides,
      mockDataGenerated: state.mockDataGenerated,
      mapRegion: state.mapRegion,
      // API State
      loading: state.loading,
      errors: state.errors,
      connection: state.connection,
      cache: state.cache,
      sync: state.sync,
    },
    setLocation: methods.setLocation,
    setCurrentRide: methods.setCurrentRide,
    addTrip: methods.addTrip,
    updateEarnings: methods.updateEarnings,
    setAvailableDrivers: methods.setAvailableDrivers,
    toggleSidebar: methods.toggleSidebar,
    setMapRegion: methods.setMapRegion,
    addPendingBooking: methods.addPendingBooking,
    removePendingBooking: methods.removePendingBooking,
    setNavigationVisibility: methods.setNavigationVisibility,
    setOnlineStatus: methods.setOnlineStatus,
    setQueueStatus: methods.setQueueStatus,
    addQueuedBooking: methods.addQueuedBooking,
    removeQueuedBooking: methods.removeQueuedBooking,
    setQueuedBookings: methods.setQueuedBookings,
    setRideStatus: methods.setRideStatus,
    shouldShowBookings: methods.shouldShowBookings,
    canAcceptNewBooking: methods.canAcceptNewBooking,
    getMaxQueueSize: methods.getMaxQueueSize,
    startNextQueuedRide: methods.startNextQueuedRide,
    setAvailableRides: methods.setAvailableRides,
    removeAvailableRide: methods.removeAvailableRide,
    generateMockRides: methods.generateMockRides,
    loadAvailableRides: methods.loadAvailableRides,
    // Unified booking management
    getAllBookings: methods.getAllBookings,
    getBookingsByStatus: methods.getBookingsByStatus,
    getBookingsByType: methods.getBookingsByType,
    getActiveBookings: methods.getActiveBookings,
    getScheduledBookings: methods.getScheduledBookings,
    acceptBooking: methods.acceptBooking,
    rejectBooking: methods.rejectBooking,
    completeBooking: methods.completeBooking,
    // API State Management
    setLoading: methods.setLoading,
    setError: methods.setError,
    clearErrors: methods.clearErrors,
    setConnectionState: methods.setConnectionState,
    isDataStale: methods.isDataStale,
    addPendingAction: methods.addPendingAction,
    clearPendingActions: methods.clearPendingActions,
    setSyncStatus: methods.setSyncStatus,
    // Data loading methods
    loadTripHistory: methods.loadTripHistory,
    loadEarningsData: methods.loadEarningsData,
    loadDriverProfile: methods.loadDriverProfile,
    refreshAllData: methods.refreshAllData,
  };
};

// New dedicated API state hook
export const useApiState = () => {
  const { state, setLoading, setError, clearErrors, setConnectionState, isDataStale, addPendingAction, clearPendingActions, setSyncStatus } = useGlobalState();
  return {
    loading: state.loading,
    errors: state.errors,
    connection: state.connection,
    cache: state.cache,
    sync: state.sync,
    setLoading,
    setError,
    clearErrors,
    setConnectionState,
    isDataStale,
    addPendingAction,
    clearPendingActions,
    setSyncStatus,
  };
};