
import { useState, useEffect, useRef } from 'react';
import * as Location from 'expo-location';
import { Location as LocationType } from '../types';

export interface LocationTrackingState {
  currentLocation: LocationType | null;
  isTracking: boolean;
  accuracy: number | null;
  speed: number | null;
  heading: number | null;
  error: string | null;
}

export interface LocationTrackingOptions {
  enableHighAccuracy?: boolean;
  trackSpeed?: boolean;
  trackHeading?: boolean;
  distanceInterval?: number;
  timeInterval?: number;
}

export const useLocationTracking = (options: LocationTrackingOptions = {}) => {
  const {
    enableHighAccuracy = true,
    trackSpeed = true,
    trackHeading = true,
    distanceInterval = 5,
    timeInterval = 5000,
  } = options;

  const [state, setState] = useState<LocationTrackingState>({
    currentLocation: null,
    isTracking: false,
    accuracy: null,
    speed: null,
    heading: null,
    error: null,
  });

  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  const startTracking = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setState((prev) => ({ ...prev, error: 'Permission to access location was denied' }));
        return false;
      }

      const locationOptions: Location.LocationOptions = {
        accuracy: enableHighAccuracy ? Location.Accuracy.High : Location.Accuracy.Balanced,
        timeInterval,
        distanceInterval,
      };

      locationSubscription.current = await Location.watchPositionAsync(
        locationOptions,
        (location) => {
          const { latitude, longitude, accuracy, speed, heading } = location.coords;
          const newLocation: LocationType = {
            latitude,
            longitude,
            address: '',
          };

          setState({
            currentLocation: newLocation,
            accuracy,
            speed: trackSpeed ? speed : null,
            heading: trackHeading ? heading : null,
            isTracking: true,
            error: null,
          });
        }
      );

      setState((prev) => ({ ...prev, isTracking: true, error: null }));
      return true;
    } catch (error) {
      setState((prev) => ({ ...prev, error: 'Failed to start location tracking', isTracking: false }));
      return false;
    }
  };

  const stopTracking = () => {
    if (locationSubscription.current) {
      locationSubscription.current.remove();
      locationSubscription.current = null;
    }
    setState((prev) => ({ ...prev, isTracking: false }));
  };

  const getCurrentLocation = async (): Promise<LocationType | null> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setState((prev) => ({ ...prev, error: 'Permission to access location was denied' }));
        return null;
      }
      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;
      return { latitude, longitude, address: '' };
    } catch (error) {
      setState((prev) => ({ ...prev, error: 'Failed to get current location' }));
      return null;
    }
  };

  useEffect(() => {
    return () => {
      stopTracking();
    };
  }, []);

  return {
    ...state,
    startTracking,
    stopTracking,
    getCurrentLocation,
  };
};
