import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Linking } from 'react-native';
import BottomSpacer from '../components/BottomSpacer';
import { MaterialIcons } from '@expo/vector-icons';
// Removed Header import
import { useNavigation } from '@react-navigation/native';
import { useApp } from '../context/ApiIntegratedGlobalStateContext';
import { colors, shadows } from '../theme/colors';

interface ActionsScreenProps {
  onNavigateToMain?: () => void;
}

const ActionsScreen: React.FC<ActionsScreenProps> = ({ onNavigateToMain }) => {
  const navigation = useNavigation();



  const handleFAQ = () => {
    Alert.alert(
      'Frequently Asked Questions',
      'Choose a category',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Account & Profile', onPress: () => console.log('Opening Account FAQs...') },
        { text: 'Payments & Earnings', onPress: () => console.log('Opening Payment FAQs...') },
        { text: 'App Features', onPress: () => console.log('Opening App Features FAQs...') }
      ]
    );
  };

  const handleTutorials = () => {
    Alert.alert(
      'Video Tutorials',
      'Choose a tutorial to watch',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Getting Started', onPress: () => console.log('Opening Getting Started tutorial...') },
        { text: 'Accepting Rides', onPress: () => console.log('Opening Accepting Rides tutorial...') },
        { text: 'Maximizing Earnings', onPress: () => console.log('Opening Earnings tutorial...') }
      ]
    );
  };

  const handleEmergency = () => {
    Alert.alert(
      'Emergency Services',
      'This will connect you to emergency services. Only use in case of emergency.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call 911', onPress: () => console.log('Calling 911...'), style: 'destructive' }
      ]
    );
  };

  const handleSupport = () => {
    Alert.alert(
      'Support Options',
      'How would you like to contact our support team?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Live Chat', onPress: () => console.log('Opening live chat...') },
        { text: 'Call Support', onPress: () => console.log('Calling support...') },
        { text: 'Email Support', onPress: () => console.log('Opening email client...') }
      ]
    );
  };



  const helpResources = [
    {
      id: 'faq',
      title: 'FAQs',
      description: 'Browse frequently asked questions',
      icon: 'help-outline' as const,
      color: colors.primary,
      onPress: handleFAQ,
      layout: 'horizontal',
    },
    {
      id: 'tutorials',
      title: 'Video Tutorials',
      description: 'Watch helpful guides and tutorials',
      icon: 'smart-display' as const,
      color: colors.success,
      onPress: handleTutorials,
      layout: 'horizontal',
    },
  ];

  const supportActions = [
    {
      id: 'support',
      title: 'Support',
      description: 'Get help from our support team',
      icon: 'support-agent' as const,
      color: colors.primary,
      onPress: handleSupport,
      priority: 'normal',
    },
    {
      id: 'assistance',
      title: 'Request Assistance',
      description: 'Get help with rides, payments, or app issues',
      icon: 'help' as const,
      color: colors.info,
      onPress: () => Alert.alert(
        'Request Assistance',
        'What do you need help with?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Technical Issues', onPress: () => console.log('Opening technical support...') },
          { text: 'Payment Problems', onPress: () => console.log('Opening payment support...') },
          { text: 'Ride Assistance', onPress: () => console.log('Opening ride assistance...') },
          { text: 'App Navigation', onPress: () => console.log('Opening app navigation help...') }
        ]
      ),
      priority: 'normal',
    },
    {
      id: 'emergency',
      title: 'Emergency',
      description: 'Contact emergency services',
      icon: 'emergency' as const,
      color: colors.error,
      onPress: handleEmergency,
      priority: 'high',
    },
  ];

  const settingsActions = [];

  const renderActionSection = (title: string, actions: Array<{id: string; title: string; description: string; icon: any; color: string; onPress: () => void; priority?: string; layout?: string}>) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {title === 'Support' ? (
        <View>
          {/* Emergency action gets special treatment if available */}
          {actions.filter(a => a.priority === 'high').map((action) => (
            <TouchableOpacity
              key={action.id}
              style={styles.emergencyCard}
              onPress={action.onPress}
              activeOpacity={0.7}
            >
              <View style={[styles.emergencyIcon, { backgroundColor: action.color + '20' }]}>
                <MaterialIcons name={action.icon} size={28} color={action.color} />
              </View>
              <View style={styles.emergencyContent}>
                <Text style={styles.emergencyTitle}>{action.title}</Text>
                <Text style={styles.emergencyDescription}>{action.description}</Text>
              </View>
            </TouchableOpacity>
          ))}
          
          {/* Regular support options */}
          <View style={styles.supportGrid}>
            {actions.filter(a => a.priority !== 'high').map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.supportCard}
                onPress={action.onPress}
                activeOpacity={0.7}
              >
                <View style={[styles.actionIcon, { backgroundColor: action.color + '20' }]}>
                  <MaterialIcons name={action.icon} size={24} color={action.color} />
                </View>
                <Text style={styles.actionTitle}>{action.title}</Text>
                <Text style={styles.actionDescription}>{action.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ) : (
        <View>
          {actions.some(a => a.layout === 'horizontal') ? (
            <View>
              {actions.map((action) => (
                <TouchableOpacity
                  key={action.id}
                  style={styles.horizontalCard}
                  onPress={action.onPress}
                  activeOpacity={0.7}
                >
                  <View style={[styles.horizontalIcon, { backgroundColor: action.color + '20' }]}>
                    <MaterialIcons name={action.icon} size={24} color={action.color} />
                  </View>
                  <View style={styles.horizontalContent}>
                    <Text style={styles.horizontalTitle}>{action.title}</Text>
                    <Text style={styles.horizontalDescription}>{action.description}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.actionGrid}>
              {actions.map((action) => (
                <TouchableOpacity
                  key={action.id}
                  style={styles.actionCard}
                  onPress={action.onPress}
                  activeOpacity={0.7}
                >
                  <View style={[styles.actionIcon, { backgroundColor: action.color + '20' }]}>
                    <MaterialIcons name={action.icon} size={24} color={action.color} />
                  </View>
                  <Text style={styles.actionTitle}>{action.title}</Text>
                  <Text style={styles.actionDescription}>{action.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      
      <ScrollView contentContainerStyle={styles.contentContainer}>
        {renderActionSection('Support', supportActions)}
        
        {/* Add bottom spacer */}
        <BottomSpacer />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 120,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 16,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  supportGrid: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  actionCard: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 16,
    width: '48%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: colors.border,
  },
  supportCard: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 16,
    flex: 1,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 140,
    justifyContent: 'center',
  },
  emergencyCard: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: colors.error + '30',
    marginBottom: 8,
  },
  horizontalCard: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 12,
  },
  emergencyIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  horizontalIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  emergencyContent: {
    flex: 1,
  },
  horizontalContent: {
    flex: 1,
  },
  emergencyTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.error,
    marginBottom: 4,
  },
  horizontalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  emergencyDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  horizontalDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  actionDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default ActionsScreen;