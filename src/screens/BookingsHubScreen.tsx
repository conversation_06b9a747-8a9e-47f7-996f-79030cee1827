import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList, Alert, Linking } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import TransferCard from '../components/TransferCard';
import BottomSpacer from '../components/BottomSpacer';
import Card from '../components/Card';
import Button from '../components/Button';
import { colors, shadows } from '../theme/colors';
import { useApp } from '../context/ApiIntegratedGlobalStateContext';
import { Ride } from '../types';

const BookingsHubScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'active' | 'pending' | 'scheduled' | 'completed' | 'rejected'>('active');
  const [expandedBookingId, setExpandedBookingId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { 
    state: appState, 
    generateMockRides,
    getAllBookings,
    getBookingsByStatus,
    getBookingsByType,
    getActiveBookings,
    getScheduledBookings,
    acceptBooking,
    rejectBooking,
    canAcceptNewBooking,
    getMaxQueueSize
  } = useApp();

  useEffect(() => {
    if (!appState.mockDataGenerated) {
      generateMockRides();
    }
  }, [appState.mockDataGenerated, generateMockRides]);

  const getFilteredBookings = (): Ride[] => {
    switch (activeTab) {
      case 'active':
        return getActiveBookings();
      case 'pending':
        return getBookingsByStatus('pending').concat(getBookingsByStatus('accepted'));
      case 'scheduled':
        return getScheduledBookings();
      case 'completed':
        return getBookingsByStatus('completed');
      case 'rejected':
        return getBookingsByStatus('rejected');
      default:
        return getAllBookings();
    }
  };

  const getStatusColor = (ride: Ride) => {
    if (ride.status === 'pending' && ride.bookingStatus === 'urgent') return colors.error;
    if (ride.status === 'accepted') return colors.primary;
    if (ride.bookingStatus === 'scheduled') return colors.primary;
    if (ride.status === 'completed') return colors.success;
    if (ride.status === 'rejected') return colors.error;
    if (ride.bookingStatus === 'premium') return colors.warning;
    return colors.textSecondary;
  };

  const getStatusText = (ride: Ride) => {
    if (ride.status === 'pending' && ride.bookingStatus === 'urgent') return 'URGENT';
    if (ride.status === 'accepted') return 'ACCEPTED';
    if (ride.bookingStatus === 'scheduled') return 'SCHEDULED';
    if (ride.status === 'completed') return 'COMPLETED';
    if (ride.status === 'rejected') return 'REJECTED';
    if (ride.bookingStatus === 'premium') return 'PREMIUM';
    if (ride.bookingStatus === 'popular') return 'POPULAR';
    return ride.bookingStatus.toUpperCase();
  };

  const handleAcceptBooking = (ride: Ride) => {
    if (!canAcceptNewBooking()) {
      if (appState.rideStatus === 'passenger_onboard' || appState.rideStatus === 'arrived_at_destination') {
        Alert.alert('Cannot Accept', 'You are currently serving a passenger. Complete your trip to accept new bookings.');
      } else if (appState.queuedBookings.length >= getMaxQueueSize()) {
        Alert.alert('Queue Full', `You already have ${appState.queuedBookings.length} bookings queued. Complete a trip to add more.`);
      } else {
        Alert.alert('Cannot Accept', 'Unable to accept booking at this time.');
      }
      return;
    }
    
    const success = acceptBooking(ride.id);
    if (success) {
      if (appState.currentRide) {
        const queuePosition = appState.queuedBookings.length + 1;
        Alert.alert(
          'Booking Queued', 
          `${ride.passengerName}'s trip has been added to your queue (#${queuePosition}/${getMaxQueueSize()}).`
        );
      }
    } else {
      Alert.alert('Error', 'Failed to accept booking. Please try again.');
    }
  };

  const handleDeclineBooking = (rideId: string) => {
    rejectBooking(rideId);
    
    if (expandedBookingId === rideId) {
      setExpandedBookingId(null);
    }
  };

  const handleCallPassenger = (phoneNumber: string, passengerName: string) => {
    Alert.alert(
      'Call Passenger',
      `Call ${passengerName} at ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Call', 
          onPress: () => {
            const phoneUrl = `tel:${phoneNumber}`;
            Linking.canOpenURL(phoneUrl)
              .then(supported => {
                if (supported) {
                  return Linking.openURL(phoneUrl);
                } else {
                  Alert.alert('Error', 'Phone calls are not supported on this device');
                }
              })
              .catch(err => {
                console.error('Error opening phone app:', err);
                Alert.alert('Error', 'Failed to open phone app');
              });
          }
        }
      ]
    );
  };

  const toggleBookingDetails = (bookingId: string) => {
    setExpandedBookingId(expandedBookingId === bookingId ? null : bookingId);
  };

  const renderBookingItem = ({ item }: { item: Ride }) => (
    <Card style={[styles.bookingCard, item.status === 'rejected' && styles.rejectedBookingCard]}>
      {/* Booking Header */}
      <View style={styles.bookingHeader}>
        <View style={styles.bookingHeaderLeft}>
          <Text style={styles.bookingTitle}>Airport Transfer</Text>
          <View style={styles.transferTypeContainer}>
            <Text style={[
              styles.transferTypeBadge,
              item.transferType === 'arrival' ? styles.arrivalBadge : styles.departureBadge
            ]}>
              {item.transferType === 'arrival' ? 'ARRIVAL' : 'DEPARTURE'}
            </Text>
          </View>
        </View>
        <View style={styles.bookingHeaderRight}>
          {item.status === 'rejected' && (
            <View style={styles.rejectedStatusBadge}>
              <MaterialIcons name="cancel" size={14} color={colors.error} />
              <Text style={styles.rejectedStatusText}>REJECTED</Text>
            </View>
          )}
          <TouchableOpacity 
            onPress={() => toggleBookingDetails(item.id)}
            style={styles.expandButton}
            activeOpacity={0.7}
          >
            <MaterialIcons 
              name={expandedBookingId === item.id ? "expand-less" : "expand-more"} 
              size={24} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Booking Info */}
      <View style={styles.bookingMainInfo}>
        <View style={styles.passengerMainRow}>
          <View style={styles.passengerMainLeft}>
            <Text style={styles.passengerNameMain}>{item.passengerName}</Text>
            <Text style={styles.flightInfoMain}>{item.flightNumber} • {item.airport}</Text>
          </View>
          <View style={styles.passengerMainRight}>
            <Text style={styles.fareAmountMain}>£{item.fare.toFixed(0)}</Text>
            <Text style={styles.fareDetailsMain}>{item.distance}km • {item.duration}min</Text>
          </View>
        </View>
        
        <View style={styles.locationMainInfo}>
          <View style={styles.locationMainRow}>
            <MaterialIcons name="location-on" size={12} color={colors.success} />
            <Text style={styles.locationMainText} numberOfLines={2}>{item.pickupLocation.address}</Text>
          </View>
          <View style={styles.locationMainRow}>
            <MaterialIcons name="place" size={12} color={colors.error} />
            <Text style={styles.locationMainText} numberOfLines={2}>{item.dropoffLocation.address}</Text>
          </View>
        </View>
      </View>

      {/* Expanded Details */}
      {expandedBookingId === item.id && (
        <View style={styles.bookingDetailsExpanded}>
          <View style={styles.expandedRow}>
            <View style={styles.expandedLeft}>
              <Text style={styles.expandedLabel}>Contact</Text>
              <Text style={styles.expandedValue}>{item.passengerPhone}</Text>
              <Text style={styles.expandedValue}>{item.passengerCount > 1 ? `${item.passengerCount} passengers` : '1 passenger'}</Text>
            </View>
            <View style={styles.expandedRight}>
              <Text style={styles.expandedLabel}>Vehicle</Text>
              <Text style={styles.expandedValue}>{item.vehicleType.replace('_', ' ')}</Text>
              <Text style={styles.expandedValue}>{item.paymentMethod.toUpperCase()}</Text>
            </View>
          </View>
          
          {item.specialRequests && (
            <View style={styles.expandedSpecialRequests}>
              <Text style={styles.expandedLabel}>Special Requests</Text>
              <Text style={styles.expandedValue}>{item.specialRequests}</Text>
            </View>
          )}
        </View>
      )}

      {/* Action Buttons */}
      {item.status === 'rejected' ? (
        <View style={styles.rejectedActionsContainer}>
          <View style={styles.rejectedStatusContainer}>
            <MaterialIcons name="info" size={16} color={colors.error} />
            <Text style={styles.rejectedMessage}>This booking was declined and is no longer available</Text>
          </View>
        </View>
      ) : (
        <View style={styles.bookingActions}>
          <Button
            title="Decline"
            onPress={() => handleDeclineBooking(item.id)}
            variant="secondary"
            style={styles.actionButton}
          />
          <Button
            title={
              !appState.currentRide ? "Accept" :
              canAcceptNewBooking() ? `Queue (${appState.queuedBookings.length + 1}/${getMaxQueueSize()})` :
              "Queue Full"
            }
            onPress={() => handleAcceptBooking(item)}
            variant={canAcceptNewBooking() ? "success" : "secondary"}
            style={styles.actionButton}
            disabled={!canAcceptNewBooking()}
          />
        </View>
      )}
    </Card>
  );

  const tabs = [
    { id: 'active', label: 'Active', icon: 'local-fire-department' as const },
    { id: 'pending', label: 'Pending', icon: 'hourglass-empty' as const },
    { id: 'scheduled', label: 'Scheduled', icon: 'schedule' as const },
    { id: 'completed', label: 'Completed', icon: 'check-circle' as const },
    { id: 'rejected', label: 'Rejected', icon: 'cancel' as const },
  ];

  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'active':
        return getActiveBookings().length;
      case 'pending':
        return getBookingsByStatus('pending').concat(getBookingsByStatus('accepted')).length;
      case 'scheduled':
        return getScheduledBookings().length;
      case 'completed':
        return getBookingsByStatus('completed').length;
      case 'rejected':
        return getBookingsByStatus('rejected').length;
      default:
        return getAllBookings().length;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsContent}
        >
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tabButton,
                activeTab === tab.id && styles.activeTabButton,
              ]}
              onPress={() => setActiveTab(tab.id as any)}
              activeOpacity={0.7}
            >
              <MaterialIcons
                name={tab.icon}
                size={20}
                color={activeTab === tab.id ? colors.primary : colors.textSecondary}
              />
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === tab.id ? colors.primary : colors.textSecondary },
                ]}
              >
                {tab.label}
              </Text>
              <View style={[styles.countBadge, activeTab === tab.id && styles.activeCountBadge]}>
                <Text style={[styles.countText, activeTab === tab.id && styles.activeCountText]}>
                  {getTabCount(tab.id)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.contentContainer}>

        {/* Booking Summary Card - Only show when there are multiple booking types */}
        {!appState.currentRide && (appState.rideStatus !== 'passenger_onboard' && appState.rideStatus !== 'arrived_at_destination') && (getActiveBookings().length + getBookingsByStatus('pending').length + getScheduledBookings().length > 3) && (
          <Card style={styles.summaryCard}>
            <View style={styles.summaryHeader}>
              <MaterialIcons name="assessment" size={20} color={colors.primary} />
              <Text style={styles.summaryTitle}>Booking Overview</Text>
            </View>
            <View style={styles.summaryStats}>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatValue}>{getActiveBookings().length}</Text>
                <Text style={styles.summaryStatLabel}>Active</Text>
              </View>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatValue}>{getBookingsByStatus('pending').length}</Text>
                <Text style={styles.summaryStatLabel}>Pending</Text>
              </View>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatValue}>{getScheduledBookings().length}</Text>
                <Text style={styles.summaryStatLabel}>Scheduled</Text>
              </View>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatValue}>{getBookingsByStatus('completed').length}</Text>
                <Text style={styles.summaryStatLabel}>Completed</Text>
              </View>
            </View>
          </Card>
        )}

        {/* Focus Mode Message - Shown when driver is in service */}
        {appState.currentRide && (appState.rideStatus === 'passenger_onboard' || appState.rideStatus === 'arrived_at_destination') && (
          <Card style={styles.focusModeCard}>
            <View style={styles.focusModeContainer}>
              <Text style={styles.focusModeIcon}>🚗</Text>
              <Text style={styles.focusModeTitle}>Focus Mode - In Service</Text>
              <Text style={styles.focusModeText}>
                Booking management is simplified while you're serving your passenger. 
                Complete your current trip to access full booking features.
              </Text>
              {appState.queuedBookings.length > 0 && (
                <Text style={styles.focusModeNote}>
                  You have {appState.queuedBookings.length} booking{appState.queuedBookings.length > 1 ? 's' : ''} queued for after this trip.
                </Text>
              )}
            </View>
          </Card>
        )}

        {activeTab === 'rejected' && getFilteredBookings().length > 0 && (
          <View style={styles.rejectedHeader}>
            <MaterialIcons name="info" size={20} color={colors.error} />
            <Text style={styles.rejectedHeaderText}>
              These are bookings you have declined. They cannot be accepted again.
            </Text>
          </View>
        )}
        
        {getFilteredBookings().length === 0 ? (
          <Card style={styles.emptyStateCard}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>🔍 Loading bookings...</Text>
                <Text style={styles.loadingSubtext}>Fetching your {activeTab} bookings</Text>
              </View>
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateIcon}>
                  {activeTab === 'active' && '🚗'}
                  {activeTab === 'pending' && '⏳'}
                  {activeTab === 'scheduled' && '📅'}
                  {activeTab === 'completed' && '✅'}
                  {activeTab === 'rejected' && '❌'}
                </Text>
                <Text style={styles.emptyStateText}>No {activeTab} bookings</Text>
                <Text style={styles.emptyStateSubtext}>
                  {activeTab === 'active' && 'Immediate bookings will appear here'}
                  {activeTab === 'pending' && 'Pending confirmations will appear here'}
                  {activeTab === 'scheduled' && 'Advance bookings will appear here'}
                  {activeTab === 'completed' && 'Completed trips will appear here'}
                  {activeTab === 'rejected' && 'Declined bookings will appear here'}
                </Text>
              </View>
            )}
          </Card>
        ) : (
          <FlatList
            data={getFilteredBookings()}
            renderItem={renderBookingItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
            ListFooterComponent={() => <BottomSpacer />}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  tabsContainer: {
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingTop: 16,
  },
  tabsContent: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: colors.backgroundTertiary,
  },
  activeTabButton: {
    backgroundColor: colors.backgroundTertiary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    marginRight: 6,
  },
  countBadge: {
    backgroundColor: '#C7C7CC',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  activeCountBadge: {
    backgroundColor: colors.primary,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  transferTypeBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  arrivalBadge: {
    backgroundColor: colors.successLight,
  },
  departureBadge: {
    backgroundColor: colors.backgroundTertiary,
  },
  transferTypeText: {
    fontSize: 10,
    fontWeight: '700',
  },
  arrivalText: {
    color: colors.success,
  },
  departureText: {
    color: colors.primary,
  },
  flightInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 6,
  },
  flightText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginRight: 8,
  },
  airportText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  durationText: {
    fontSize: 12,
    color: colors.textTertiary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginLeft: 4,
  },
  rejectedBookingCard: {
    opacity: 0.7,
    borderWidth: 1,
    borderColor: colors.error,
    backgroundColor: colors.errorLight,
  },
  rejectedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 10,
    borderRadius: 8,
    marginBottom: 12,
    gap: 6,
    borderLeftWidth: 3,
    borderLeftColor: colors.error,
  },
  rejectedInfoText: {
    flex: 1,
    fontSize: 12,
    color: colors.error,
    fontWeight: '500',
  },
  rejectedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 16,
    marginHorizontal: 12,
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: colors.error,
  },
  rejectedHeaderText: {
    flex: 1,
    fontSize: 14,
    color: colors.error,
    fontWeight: '500',
    lineHeight: 20,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textLight,
  },
  activeCountText: {
    color: colors.textLight,
  },
  contentContainer: {
    flex: 1,
  },
  listContainer: {
    padding: 20,
    paddingBottom: 20, // Reduced padding since we'll add the BottomSpacer
  },
  bookingCard: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: colors.border,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 6,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '700',
  },
  routeInfo: {
    marginBottom: 12,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  locationText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
    flex: 1,
  },
  routeArrow: {
    alignItems: 'center',
    marginVertical: 2,
  },
  scheduledTime: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginBottom: 12,
  },
  scheduledTimeText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 6,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  fareInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fareAmount: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.success,
    marginRight: 8,
  },
  distanceText: {
    fontSize: 12,
    color: colors.textTertiary,
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: colors.textTertiary,
    marginRight: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textTertiary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#C7C7CC',
    textAlign: 'center',
    lineHeight: 20,
  },
  // Dashboard-style improvements
  summaryCard: {
    marginHorizontal: 12,
    marginTop:8
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    paddingVertical: 16,
  },
  summaryStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  summaryStatLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  emptyStateCard: {
    marginHorizontal: 12,
    marginTop: 20,
  },
  emptyStateIcon: {
    fontSize: 48,
    textAlign: 'center',
    marginBottom: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: colors.primary,
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Enhanced booking card styles
  bookingCard: {
    marginHorizontal: 12,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  bookingHeaderLeft: {
    flex: 1,
  },
  bookingHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bookingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  transferTypeContainer: {
    flexDirection: 'row',
  },
  transferTypeBadge: {
    fontSize: 10,
    fontWeight: '700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    textAlign: 'center',
  },
  expandButton: {
    padding: 4,
  },
  rejectedStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    borderWidth: 1,
    borderColor: colors.error,
  },
  rejectedStatusText: {
    fontSize: 10,
    fontWeight: '700',
    color: colors.error,
  },
  rejectedBookingCard: {
    opacity: 0.7,
    borderWidth: 2,
    borderColor: colors.error,
    backgroundColor: colors.errorLight,
  },
  bookingMainInfo: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 14,
    marginBottom: 12,
  },
  passengerMainRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  passengerMainLeft: {
    flex: 2,
  },
  passengerMainRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  passengerNameMain: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  flightInfoMain: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
  },
  fareAmountMain: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 2,
  },
  fareDetailsMain: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  locationMainInfo: {
    gap: 6,
  },
  locationMainRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationMainText: {
    fontSize: 12,
    color: colors.textSecondary,
    flex: 1,
  },
  bookingDetailsExpanded: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  expandedRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  expandedLeft: {
    flex: 1,
  },
  expandedRight: {
    flex: 1,
  },
  expandedLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 4,
  },
  expandedValue: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  expandedSpecialRequests: {
    marginBottom: 12,
    padding: 10,
    backgroundColor: colors.warningLight,
    borderRadius: 8,
  },
  communicationActions: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.success,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  messageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  bookingActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  rejectedActionsContainer: {
    marginBottom: 8,
  },
  rejectedStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.errorLight,
    padding: 12,
    borderRadius: 8,
    gap: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
  },
  rejectedMessage: {
    flex: 1,
    fontSize: 14,
    color: colors.error,
    fontWeight: '500',
  },
  // Focus mode styles
  focusModeCard: {
    marginHorizontal: 12,
    marginTop: 16,
    marginBottom: 8,
  },
  focusModeContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  focusModeIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  focusModeTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  focusModeText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 8,
  },
  focusModeNote: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 4,
  },
});

export default BookingsHubScreen;