import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import BottomSpacer from '../components/BottomSpacer';
import { useNavigation } from '@react-navigation/native';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';
import Card from '../components/Card';
import { DriverStats } from '../types';
import { colors } from '../theme/colors';
const EarningsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state: appState } = useApp();
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');

  const todayEarnings = appState.trips
    .filter(trip => {
      const today = new Date();
      const tripDate = new Date(trip.completedAt);
      return tripDate.toDateString() === today.toDateString();
    })
    .reduce((sum, trip) => sum + trip.earnings, 0);

  const weekEarnings = appState.trips
    .filter(trip => {
      const today = new Date();
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const tripDate = new Date(trip.completedAt);
      return tripDate >= weekAgo && tripDate <= today;
    })
    .reduce((sum, trip) => sum + trip.earnings, 0);

  const monthEarnings = appState.trips
    .filter(trip => {
      const today = new Date();
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      const tripDate = new Date(trip.completedAt);
      return tripDate >= monthAgo && tripDate <= today;
    })
    .reduce((sum, trip) => sum + trip.earnings, 0);

  const todayTrips = appState.trips.filter(trip => {
    const today = new Date();
    const tripDate = new Date(trip.completedAt);
    return tripDate.toDateString() === today.toDateString();
  }).length;

  const weekTrips = appState.trips.filter(trip => {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const tripDate = new Date(trip.completedAt);
    return tripDate >= weekAgo && tripDate <= today;
  }).length;

  const monthTrips = appState.trips.filter(trip => {
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    const tripDate = new Date(trip.completedAt);
    return tripDate >= monthAgo && tripDate <= today;
  }).length;

  const getEarningsForPeriod = () => {
    switch (selectedPeriod) {
      case 'today':
        return todayEarnings;
      case 'week':
        return weekEarnings;
      case 'month':
        return monthEarnings;
      default:
        return todayEarnings;
    }
  };

  const getTripsForPeriod = () => {
    switch (selectedPeriod) {
      case 'today':
        return todayTrips;
      case 'week':
        return weekTrips;
      case 'month':
        return monthTrips;
      default:
        return todayTrips;
    }
  };

  const averagePerTrip = getTripsForPeriod() > 0 ? getEarningsForPeriod() / getTripsForPeriod() : 0;

  const driverStats: DriverStats = {
    totalTrips: appState.trips.length,
    totalEarnings: appState.totalEarnings,
    acceptanceRate: 85, // Mock data
    cancellationRate: 2, // Mock data
    onlineHours: 8.5, // Mock data
  };

  // Navigation is now handled by AppNavigator

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer}>

      <View style={styles.periodSelector}>
        {(['today', 'week', 'month'] as const).map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period && styles.periodButtonTextActive
            ]}>
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Card style={styles.earningsCard}>
        <Text style={styles.earningsTitle}>
          {selectedPeriod === 'today' ? "Today's" : selectedPeriod === 'week' ? "This Week's" : "This Month's"} Earnings
        </Text>
        <Text style={styles.earningsAmount}>£{getEarningsForPeriod().toFixed(2)}</Text>
        <View style={styles.earningsStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{getTripsForPeriod()}</Text>
            <Text style={styles.statLabel}>Trips</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>£{averagePerTrip.toFixed(2)}</Text>
            <Text style={styles.statLabel}>Avg/Trip</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{driverStats.onlineHours}h</Text>
            <Text style={styles.statLabel}>Online</Text>
          </View>
        </View>
      </Card>

      <View style={styles.statsGrid}>
        <Card style={styles.statCard}>
          <Text style={styles.statCardTitle}>Total Earnings</Text>
          <Text style={styles.statCardValue}>£{driverStats.totalEarnings.toFixed(2)}</Text>
          <Text style={styles.statCardSubtext}>All time</Text>
        </Card>

        <Card style={styles.statCard}>
          <Text style={styles.statCardTitle}>Total Trips</Text>
          <Text style={styles.statCardValue}>{driverStats.totalTrips}</Text>
          <Text style={styles.statCardSubtext}>Completed</Text>
        </Card>


        <Card style={styles.statCard}>
          <Text style={styles.statCardTitle}>Acceptance Rate</Text>
          <Text style={styles.statCardValue}>{driverStats.acceptanceRate}%</Text>
          <Text style={styles.statCardSubtext}>Last 30 days</Text>
        </Card>
      </View>

      <Card>
        <Text style={styles.sectionTitle}>Performance Metrics</Text>
        
        <View style={styles.metricRow}>
          <View style={styles.metricInfo}>
            <Text style={styles.metricLabel}>Acceptance Rate</Text>
            <Text style={styles.metricSubtext}>How often you accept rides</Text>
          </View>
          <View style={styles.metricValueContainer}>
            <View style={[styles.progressBar, { width: `${driverStats.acceptanceRate}%` }]} />
            <Text style={styles.metricValue}>{driverStats.acceptanceRate}%</Text>
          </View>
        </View>

        <View style={styles.metricRow}>
          <View style={styles.metricInfo}>
            <Text style={styles.metricLabel}>Cancellation Rate</Text>
            <Text style={styles.metricSubtext}>How often you cancel rides</Text>
          </View>
          <View style={styles.metricValueContainer}>
            <View style={[styles.progressBar, styles.progressBarDanger, { width: `${driverStats.cancellationRate}%` }]} />
            <Text style={styles.metricValue}>{driverStats.cancellationRate}%</Text>
          </View>
        </View>

      </Card>

      <Card>
        <Text style={styles.sectionTitle}>Quick Tips</Text>
        <View style={styles.tipItem}>
          <Text style={styles.tipEmoji}>💡</Text>
          <Text style={styles.tipText}>Stay online during peak hours (7-9 AM, 5-7 PM) to maximize earnings</Text>
        </View>
        <View style={styles.tipItem}>
          <Text style={styles.tipEmoji}>🎯</Text>
          <Text style={styles.tipText}>Keep your acceptance rate above 80% for better visibility</Text>
        </View>
      </Card>
      
      {/* Add bottom spacer */}
      <BottomSpacer />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 120,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: colors.border,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: colors.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  periodButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  periodButtonTextActive: {
    color: colors.text,
  },
  earningsCard: {
    alignItems: 'center',
    marginBottom: 20,
  },
  earningsTitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 8,
  },
  earningsAmount: {
    fontSize: 48,
    fontWeight: 'bold',
    color: colors.success,
    marginBottom: 20,
  },
  earningsStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 12,
  },
  statCardTitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
  },
  statCardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  statCardSubtext: {
    fontSize: 12,
    color: colors.textTertiary,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  metricInfo: {
    flex: 1,
  },
  metricLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  metricSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  metricValueContainer: {
    width: 80,
    alignItems: 'flex-end',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#34C759',
    borderRadius: 3,
    marginBottom: 4,
  },
  progressBarDanger: {
    backgroundColor: '#FF3B30',
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  tipEmoji: {
    fontSize: 20,
    marginRight: 12,
    marginTop: 2,
  },
  tipText: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    lineHeight: 22,
  },
});

export default EarningsScreen;