import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, FlatList, TouchableOpacity } from 'react-native';
import BottomSpacer from '../components/BottomSpacer';
import { useNavigation } from '@react-navigation/native';
import { useAuth, useApp } from '../context/ApiIntegratedGlobalStateContext';
import Button from '../components/Button';
import Input from '../components/Input';
import Card from '../components/Card';
// Removed Header import
import { Trip } from '../types';
import { colors, shadows } from '../theme/colors';

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state: authState, updateDriver, logout } = useAuth();
  const { state: appState } = useApp();
  const [editing, setEditing] = useState(false);
  const [activeTab, setActiveTab] = useState<'profile' | 'history'>('profile');
  const [formData, setFormData] = useState({
    name: authState.driver?.name || '',
    phone: authState.driver?.phone || '',
    vehicleType: authState.driver?.vehicleType || '',
    licensePlate: authState.driver?.licensePlate || '',
  });

  const handleSave = () => {
    if (authState.driver) {
      const updatedDriver = {
        ...authState.driver,
        ...formData,
      };
      updateDriver(updatedDriver);
      setEditing(false);
      Alert.alert('Success', 'Profile updated successfully');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const handleCancel = () => {
    setFormData({
      name: authState.driver?.name || '',
      phone: authState.driver?.phone || '',
      vehicleType: authState.driver?.vehicleType || '',
      licensePlate: authState.driver?.licensePlate || '',
    });
    setEditing(false);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getWeeklyEarnings = () => {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    return appState.trips
      .filter(trip => new Date(trip.completedAt) >= weekAgo)
      .reduce((sum, trip) => sum + trip.earnings, 0);
  };

  const renderTripItem = ({ item }: { item: Trip }) => (
    <Card style={styles.tripCard}>
      <View style={styles.tripHeader}>
        <Text style={styles.tripDate}>{formatDate(item.completedAt)}</Text>
        <Text style={styles.tripEarnings}>+£{item.earnings.toFixed(2)}</Text>
      </View>
      <View style={styles.tripDetails}>
        <Text style={styles.tripDistance}>{item.distance}km</Text>
        <Text style={styles.tripDuration}>{item.duration}min</Text>
      </View>
    </Card>
  );

  // Navigation is now handled by AppNavigator

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer}>
        <Card>
        <View style={styles.header}>
          <Text style={styles.title}>Driver Profile</Text>
          {!editing && activeTab === 'profile' && (
            <Button
              title="Edit"
              onPress={() => setEditing(true)}
              variant="secondary"
              style={styles.editButton}
            />
          )}
        </View>

      </Card>

      <Card>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'profile' && styles.activeTab]}
            onPress={() => setActiveTab('profile')}
          >
            <Text style={[styles.tabText, activeTab === 'profile' && styles.activeTabText]}>Profile</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'history' && styles.activeTab]}
            onPress={() => setActiveTab('history')}
          >
            <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>Trip History</Text>
          </TouchableOpacity>
        </View>
      </Card>

      {activeTab === 'profile' ? (
        <>
          <Card>
            <Text style={styles.sectionTitle}>Personal Information</Text>
            
            <Input
              label="Full Name"
              value={formData.name}
              onChangeText={(value) => setFormData(prev => ({ ...prev, name: value }))}
              editable={editing}
              style={!editing && styles.readOnlyInput}
            />
            
            <Input
              label="Email"
              value={authState.driver?.email || ''}
              editable={false}
              style={styles.readOnlyInput}
            />
            
            <Input
              label="Phone Number"
              value={formData.phone}
              onChangeText={(value) => setFormData(prev => ({ ...prev, phone: value }))}
              editable={editing}
              style={!editing && styles.readOnlyInput}
              keyboardType="phone-pad"
            />
          </Card>

          <Card>
            <Text style={styles.sectionTitle}>Vehicle Information</Text>
            
            <Input
              label="Vehicle Type"
              value={formData.vehicleType}
              onChangeText={(value) => setFormData(prev => ({ ...prev, vehicleType: value }))}
              editable={editing}
              style={!editing && styles.readOnlyInput}
              placeholder="e.g., Toyota Camry"
            />
            
            <Input
              label="License Plate"
              value={formData.licensePlate}
              onChangeText={(value) => setFormData(prev => ({ ...prev, licensePlate: value }))}
              editable={editing}
              style={!editing && styles.readOnlyInput}
              placeholder="e.g., ABC-123"
              autoCapitalize="characters"
            />
          </Card>

          {editing && (
            <View style={styles.editActions}>
              <Button
                title="Cancel"
                onPress={handleCancel}
                variant="secondary"
                style={styles.actionButton}
              />
              <Button
                title="Save"
                onPress={handleSave}
                variant="success"
                style={styles.actionButton}
              />
            </View>
          )}

          <Card>
            <Text style={styles.sectionTitle}>Account Actions</Text>
            <Button
              title="Logout"
              onPress={handleLogout}
              variant="danger"
              style={styles.logoutButton}
            />
          </Card>
        </>
      ) : (
        <>
          <Card>
            <Text style={styles.sectionTitle}>Earnings Summary</Text>
            <View style={styles.earningsRow}>
              <View style={styles.earningItem}>
                <Text style={styles.earningAmount}>£{appState.totalEarnings.toFixed(2)}</Text>
                <Text style={styles.earningLabel}>Total Earnings</Text>
              </View>
              <View style={styles.earningItem}>
                <Text style={styles.earningAmount}>£{getWeeklyEarnings().toFixed(2)}</Text>
                <Text style={styles.earningLabel}>This Week</Text>
              </View>
            </View>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{appState.trips.length}</Text>
                <Text style={styles.statLabel}>Total Trips</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {appState.trips.length > 0 
                    ? (appState.totalEarnings / appState.trips.length).toFixed(2)
                    : '0.00'
                  }
                </Text>
                <Text style={styles.statLabel}>Avg per Trip</Text>
              </View>
            </View>
          </Card>

          <Card>
            <Text style={styles.sectionTitle}>Recent Trips</Text>
            
            {appState.trips.length === 0 ? (
              <Text style={styles.noTripsText}>No trips completed yet</Text>
            ) : (
              <FlatList
                data={appState.trips}
                renderItem={renderTripItem}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            )}
          </Card>
        </>
      )}
      
      {/* Add bottom spacer */}
      <BottomSpacer />
      </ScrollView>
      
      {/* Sidebar removed - now using AppNavigator */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
  },
  editButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 36,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  readOnlyInput: {
    backgroundColor: '#F8F8F8',
    color: colors.textSecondary,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 16,
  },
  actionButton: {
    flex: 1,
  },
  logoutButton: {
    marginTop: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 8,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: colors.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  activeTabText: {
    color: colors.text,
  },
  earningsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  earningItem: {
    alignItems: 'center',
    flex: 1,
  },
  earningAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.success,
    marginBottom: 4,
  },
  earningLabel: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 4,
  },
  tripCard: {
    marginBottom: 12,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripDate: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  tripEarnings: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.success,
  },
  tripDetails: {
    flexDirection: 'row',
    gap: 16,
  },
  tripDistance: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  tripDuration: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  noTripsText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ProfileScreen;