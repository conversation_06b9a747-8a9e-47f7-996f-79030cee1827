import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG } from '../config/api.config';

// Types for API responses
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

export interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean;
  retries?: number;
  cacheKey?: string;
  cacheDuration?: number;
}

// Token storage keys
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const TOKEN_EXPIRY_KEY = 'token_expiry';

class ApiService {
  private static instance: ApiService;
  private axiosInstance: AxiosInstance;
  private isRefreshing = false;
  private refreshSubscribers: ((token: string) => void)[] = [];
  private cache = new Map<string, { data: any; expiry: number }>();

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // Add authorization header if not skipped
        if (!config.skipAuth) {
          const token = await this.getStoredToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        // Add request timestamp for logging
        config.metadata = { requestTimestamp: Date.now() };

        if (API_CONFIG.isDevelopment) {
          console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        }

        return config;
      },
      (error) => {
        console.error('📛 Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        const duration = Date.now() - response.config.metadata?.requestTimestamp;
        
        if (API_CONFIG.isDevelopment) {
          console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
        }

        // Cache response if cache key is provided
        if (response.config.cacheKey && response.config.cacheDuration) {
          this.setCache(response.config.cacheKey, response.data, response.config.cacheDuration);
        }

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as RequestConfig;

        if (API_CONFIG.isDevelopment) {
          console.error('❌ API Error:', error.response?.status, error.response?.statusText);
        }

        // Handle 401 Unauthorized - attempt token refresh
        if (error.response?.status === 401 && !originalRequest.skipAuth && !originalRequest._retry) {
          if (this.isRefreshing) {
            // Wait for refresh to complete
            return new Promise((resolve) => {
              this.refreshSubscribers.push((token: string) => {
                originalRequest.headers!.Authorization = `Bearer ${token}`;
                resolve(this.axiosInstance(originalRequest));
              });
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const newToken = await this.refreshToken();
            this.onTokenRefreshed(newToken);
            originalRequest.headers!.Authorization = `Bearer ${newToken}`;
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            this.onTokenRefreshFailed();
            return Promise.reject(refreshError);
          }
        }

        // Handle network errors with retry
        if (this.shouldRetry(error, originalRequest)) {
          return this.retryRequest(originalRequest);
        }

        return Promise.reject(this.formatError(error));
      }
    );
  }

  private shouldRetry(error: AxiosError, config: RequestConfig): boolean {
    const retryCount = config.retries || 0;
    const maxRetries = API_CONFIG.RETRY_ATTEMPTS;
    
    // Don't retry if we've exceeded max attempts
    if (retryCount >= maxRetries) {
      return false;
    }

    // Retry for network errors or 5xx server errors
    return !error.response || (error.response.status >= 500 && error.response.status <= 599);
  }

  private async retryRequest(config: RequestConfig): Promise<AxiosResponse> {
    const retryCount = (config.retries || 0) + 1;
    const delay = API_CONFIG.RETRY_DELAY * Math.pow(2, retryCount - 1); // Exponential backoff

    await this.sleep(delay);

    return this.axiosInstance({
      ...config,
      retries: retryCount,
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private formatError(error: AxiosError): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.message || error.response.statusText || 'Server Error',
        status: error.response.status,
        code: error.response.data?.code,
        details: error.response.data,
      };
    } else if (error.request) {
      // Network error
      return {
        message: 'Network Error - Please check your connection',
        status: 0,
        code: 'NETWORK_ERROR',
      };
    } else {
      // Request setup error
      return {
        message: error.message || 'Request Error',
        status: 0,
        code: 'REQUEST_ERROR',
      };
    }
  }

  // Token management
  private async getStoredToken(): Promise<string | null> {
    try {
      const token = await AsyncStorage.getItem(TOKEN_KEY);
      const expiry = await AsyncStorage.getItem(TOKEN_EXPIRY_KEY);
      
      if (token && expiry) {
        const expiryTime = parseInt(expiry, 10);
        if (Date.now() < expiryTime) {
          return token;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  private async refreshToken(): Promise<string> {
    try {
      const refreshToken = await AsyncStorage.getItem(REFRESH_TOKEN_KEY);
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await this.axiosInstance.post(API_CONFIG.ENDPOINTS.AUTH.REFRESH, {
        refreshToken,
      }, { skipAuth: true });

      const { token, refreshToken: newRefreshToken, expiresIn } = response.data;
      
      await this.storeTokens(token, newRefreshToken, expiresIn);
      return token;
    } catch (error) {
      await this.clearTokens();
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  private onTokenRefreshed(token: string): void {
    this.refreshSubscribers.forEach(callback => callback(token));
    this.refreshSubscribers = [];
  }

  private onTokenRefreshFailed(): void {
    this.refreshSubscribers = [];
    this.clearTokens();
  }

  // Public methods for token management
  public async storeTokens(token: string, refreshToken: string, expiresIn: number): Promise<void> {
    try {
      const expiryTime = Date.now() + (expiresIn * 1000);
      
      await AsyncStorage.multiSet([
        [TOKEN_KEY, token],
        [REFRESH_TOKEN_KEY, refreshToken],
        [TOKEN_EXPIRY_KEY, expiryTime.toString()],
      ]);
    } catch (error) {
      console.error('Error storing tokens:', error);
      throw error;
    }
  }

  public async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([TOKEN_KEY, REFRESH_TOKEN_KEY, TOKEN_EXPIRY_KEY]);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }

  public async isAuthenticated(): Promise<boolean> {
    const token = await this.getStoredToken();
    return !!token;
  }

  // Cache management
  private setCache(key: string, data: any, duration: number): void {
    const expiry = Date.now() + duration;
    this.cache.set(key, { data, expiry });
  }

  private getCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() < cached.expiry) {
      return cached.data;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  public clearCache(pattern?: string): void {
    if (pattern) {
      // Clear cache entries matching pattern
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // HTTP methods
  public async get<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    // Check cache first
    if (config?.cacheKey) {
      const cached = this.getCache(config.cacheKey);
      if (cached) {
        return cached;
      }
    }

    const response = await this.axiosInstance.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  public async post<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  public async put<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  public async patch<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  // File upload
  public async uploadFile<T>(
    url: string,
    file: File | Blob,
    fieldName: string = 'file',
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append(fieldName, file);

    const config: RequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total!);
        onProgress(percentCompleted);
      };
    }

    const response = await this.axiosInstance.post<ApiResponse<T>>(url, formData, config);
    return response.data;
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health', { skipAuth: true, timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  // Request cancellation
  public createCancelToken() {
    return axios.CancelToken.source();
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();
export default ApiService;