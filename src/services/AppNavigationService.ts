import { createRef } from 'react';
import { NavigationContainerRef } from '@react-navigation/native';

// Create a navigation reference that can be used outside of React components
export const navigationRef = createRef<NavigationContainerRef<any>>();

// Navigate to a specific screen
export function navigate(name: string, params?: any) {
  if (navigationRef.current) {
    navigationRef.current.navigate(name, params);
  } else {
    // Save the navigation action for when the navigator is ready
    pendingNavigationAction = { name, params };
  }
}

// Store a pending navigation action
let pendingNavigationAction: { name: string; params?: any } | null = null;

// Execute any pending navigation actions
export function executePendingNavigation() {
  if (pendingNavigationAction && navigationRef.current) {
    const { name, params } = pendingNavigationAction;
    navigationRef.current.navigate(name, params);
    pendingNavigationAction = null;
  }
}

// Reset the navigation state
export function resetNavigation(state: any) {
  if (navigationRef.current) {
    navigationRef.current.reset(state);
  }
}

// Go back to the previous screen
export function goBack() {
  if (navigationRef.current && navigationRef.current.canGoBack()) {
    navigationRef.current.goBack();
  }
}

// Navigate to a specific screen in the drawer navigator
export function navigateToTab(screenName: string) {
  if (navigationRef.current) {
    try {
      // Check if we're authenticated (on the Main drawer navigator)
      const currentState = navigationRef.current.getRootState();
      const isOnMainScreen = currentState?.routes?.some(route => route.name === 'Main');
      
      if (isOnMainScreen) {
        // Navigate directly to the screen in the drawer
        navigationRef.current.navigate('Main', { screen: screenName });
      } else {
        // If not on Main yet, navigate to Main first
        navigationRef.current.navigate('Main', { screen: screenName });
      }
    } catch (error) {
      console.error('Navigation error:', error);
      
      // Fallback approach - try direct navigation
      try {
        navigationRef.current.navigate(screenName);
      } catch (innerError) {
        console.error('Fallback navigation error:', innerError);
      }
    }
  }
}

// Open the drawer
export function openDrawer() {
  if (navigationRef.current) {
    try {
      // Use dispatch to open drawer
      navigationRef.current.dispatch({ type: 'OPEN_DRAWER' });
    } catch (error) {
      console.error('Error opening drawer:', error);
    }
  }
}

// Close the drawer  
export function closeDrawer() {
  if (navigationRef.current) {
    try {
      // Use dispatch to close drawer
      navigationRef.current.dispatch({ type: 'CLOSE_DRAWER' });
    } catch (error) {
      console.error('Error closing drawer:', error);
    }
  }
}