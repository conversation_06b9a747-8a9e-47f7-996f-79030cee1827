import { apiService, ApiResponse } from './ApiService';
import { API_CONFIG } from '../config/api.config';
import { Driver } from '../types';
import { mockData } from '../data/mockData';

// Authentication request/response types
export interface LoginRequest {
  email: string;
  password: string;
  deviceInfo?: {
    deviceId: string;
    platform: string;
    version: string;
  };
}

export interface LoginResponse {
  driver: Driver;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LogoutRequest {
  refreshToken?: string;
  deviceId?: string;
}

export interface BiometricAuthRequest {
  driverId: string;
  biometricToken: string;
  deviceId: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirmRequest {
  email: string;
  resetToken: string;
  newPassword: string;
}

class AuthService {
  private static instance: AuthService;

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Login with email and password
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    // Always use real API for login
    console.log('🔑 Using real Laravel API login');
    return this.realApiLogin(request);
  }

  /**
   * Real API login for Laravel backend
   */
  private async realApiLogin(request: LoginRequest): Promise<LoginResponse> {
    try {
      // Laravel API expects email and password
      const loginPayload = {
        email: request.email,
        password: request.password
      };

      const response: ApiResponse<any> = await apiService.post(
        API_CONFIG.ENDPOINTS.AUTH.LOGIN,
        loginPayload,
        { skipAuth: true }
      );

      if (response.success && response.data) {
        // Transform Laravel response to our LoginResponse format
        const mockDriver = mockData.generateDriver('api-driver-' + Date.now());
        mockDriver.email = request.email;
        
        // Use token from Laravel API or create one if not provided
        const token = response.data.token || response.data.access_token || 'laravel-token-' + Date.now();
        const refreshToken = response.data.refresh_token || 'refresh-' + Date.now();
        const expiresIn = response.data.expires_in || 3600;

        const loginResponse: LoginResponse = {
          driver: mockDriver,
          token: token,
          refreshToken: refreshToken,
          expiresIn: expiresIn,
        };

        // Store tokens in secure storage
        await apiService.storeTokens(
          loginResponse.token,
          loginResponse.refreshToken,
          loginResponse.expiresIn
        );

        console.log('✅ Real API login successful for:', request.email);
        return loginResponse;
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      // Log the error but don't fall back to mock for real API calls
      console.error('Real API login failed:', error);
      throw new Error(error.response?.data?.message || error.message || 'Login failed');
    }
  }

  /**
   * Mock login for development/testing
   */
  private async mockLogin(request: LoginRequest): Promise<LoginResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Simple validation - accept any email/password combo for dev
    if (!request.email || !request.password) {
      throw new Error('Email and password are required');
    }

    // Use our comprehensive mock driver data
    const mockDriver = mockData.generateDriver('mock-driver-123');
    mockDriver.email = request.email; // Use the provided email
    
    console.log('✅ Mock login successful for:', request.email);

    const mockToken = 'mock-jwt-token-' + Date.now();
    const mockRefreshToken = 'mock-refresh-token-' + Date.now();
    
    const loginResponse: LoginResponse = {
      driver: mockDriver,
      token: mockToken,
      refreshToken: mockRefreshToken,
      expiresIn: 3600, // 1 hour
    };

    // Store tokens
    await apiService.storeTokens(
      loginResponse.token,
      loginResponse.refreshToken,
      loginResponse.expiresIn
    );

    return loginResponse;
  }

  /**
   * Logout and clear session
   */
  async logout(request?: LogoutRequest): Promise<void> {
    try {
      // For now, just clear local tokens (keep other functionality as mock)
      console.log('🎭 Mock logout - clearing local tokens only');
      await apiService.clearTokens();
    } catch (error) {
      console.warn('Logout cleanup failed:', error);
      // Still try to clear tokens
      await apiService.clearTokens();
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      const response: ApiResponse<RefreshTokenResponse> = await apiService.post(
        API_CONFIG.ENDPOINTS.AUTH.REFRESH,
        { refreshToken },
        { skipAuth: true }
      );

      if (response.success && response.data) {
        // Update stored tokens
        await apiService.storeTokens(
          response.data.token,
          response.data.refreshToken,
          response.data.expiresIn
        );

        return response.data;
      } else {
        throw new Error(response.message || 'Token refresh failed');
      }
    } catch (error: any) {
      // Clear tokens on refresh failure
      await apiService.clearTokens();
      throw new Error(error.message || 'Token refresh failed');
    }
  }

  /**
   * Verify current token validity
   */
  async verifyToken(): Promise<boolean> {
    // Keep token verification as mock for now (only login uses real API)
    console.log('🎭 Mock: Token verification - always valid in mock mode');
    return true; // Always return true in mock mode
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      return await apiService.isAuthenticated();
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current driver profile
   */
  async getCurrentDriver(): Promise<Driver | null> {
    // Keep driver profile as mock for now (only login uses real API)
    console.log('🎭 Mock: Getting current driver profile');
    return mockData.generateDriver('mock-driver-123');
  }

  /**
   * Biometric authentication (if enabled)
   */
  async biometricLogin(request: BiometricAuthRequest): Promise<LoginResponse> {
    if (!API_CONFIG.FEATURES.BIOMETRIC_AUTH) {
      throw new Error('Biometric authentication is not enabled');
    }

    try {
      const response: ApiResponse<LoginResponse> = await apiService.post(
        '/auth/biometric',
        request,
        { skipAuth: true }
      );

      if (response.success && response.data) {
        await apiService.storeTokens(
          response.data.token,
          response.data.refreshToken,
          response.data.expiresIn
        );

        return response.data;
      } else {
        throw new Error(response.message || 'Biometric login failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Biometric authentication failed');
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(request: PasswordResetRequest): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        '/auth/password-reset',
        request,
        { skipAuth: true }
      );

      if (!response.success) {
        throw new Error(response.message || 'Password reset request failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to request password reset');
    }
  }

  /**
   * Confirm password reset with token
   */
  async confirmPasswordReset(request: PasswordResetConfirmRequest): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        '/auth/password-reset/confirm',
        request,
        { skipAuth: true }
      );

      if (!response.success) {
        throw new Error(response.message || 'Password reset failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to reset password');
    }
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        '/auth/change-password',
        { currentPassword, newPassword }
      );

      if (!response.success) {
        throw new Error(response.message || 'Password change failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to change password');
    }
  }

  /**
   * Update device registration for push notifications
   */
  async updateDeviceToken(deviceToken: string, platform: 'ios' | 'android'): Promise<void> {
    try {
      const response: ApiResponse<{ message: string }> = await apiService.post(
        '/auth/device-token',
        { deviceToken, platform }
      );

      if (!response.success) {
        throw new Error(response.message || 'Device token update failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update device token');
    }
  }

  /**
   * Get authentication status with additional details
   */
  async getAuthStatus(): Promise<{
    isAuthenticated: boolean;
    tokenValid: boolean;
    driver: Driver | null;
    expiresAt: Date | null;
  }> {
    // Keep auth status as mock for now (only login uses real API)
    console.log('🎭 Mock: Getting auth status - returning authenticated state');
    
    try {
      const isAuthenticated = await this.isAuthenticated();
      
      if (!isAuthenticated) {
        return {
          isAuthenticated: false,
          tokenValid: false,
          driver: null,
          expiresAt: null,
        };
      }

      return {
        isAuthenticated: true,
        tokenValid: true,
        driver: mockData.generateDriver('mock-driver-123'),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      };
    } catch (error) {
      return {
        isAuthenticated: false,
        tokenValid: false,
        driver: null,
        expiresAt: null,
      };
    }
  }

  /**
   * Silent refresh - attempt to refresh token without user interaction
   */
  async silentRefresh(): Promise<boolean> {
    try {
      // This would be called by ApiService automatically
      // but can be used manually if needed
      return await this.verifyToken();
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
export default AuthService;