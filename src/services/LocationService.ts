import * as Location from 'expo-location';
import { apiService, ApiResponse } from './ApiService';
import { API_CONFIG } from '../config/api.config';
import { Location as LocationType } from '../types';

// Enhanced location service types
export interface NavigationRoute {
  distance: number; // meters
  duration: number; // seconds
  polyline: string;
  waypoints: LocationType[];
  instructions: NavigationInstruction[];
  trafficDelaySeconds?: number;
}

export interface NavigationInstruction {
  instruction: string;
  distance: number;
  duration: number;
  location: LocationType;
  maneuver: string;
}

export interface ETARequest {
  from: LocationType;
  to: LocationType;
  includeTraffic?: boolean;
  departureTime?: Date;
}

export interface ETAResponse {
  distance: number;
  duration: number;
  durationInTraffic?: number;
  route: NavigationRoute;
}

export interface GeocodingRequest {
  address: string;
  bounds?: {
    northeast: LocationType;
    southwest: LocationType;
  };
  language?: string;
}

export interface GeocodingResponse {
  results: Array<{
    location: LocationType;
    formattedAddress: string;
    addressComponents: {
      shortName: string;
      longName: string;
      types: string[];
    }[];
    placeId: string;
  }>;
}

export interface ReverseGeocodingResponse {
  results: Array<{
    formattedAddress: string;
    addressComponents: {
      shortName: string;
      longName: string;
      types: string[];
    }[];
    placeId: string;
    types: string[];
  }>;
}

export class LocationService {
  private static instance: LocationService;
  private watchId: Location.LocationSubscription | null = null;
  private lastKnownLocation: Location.LocationObject | null = null;
  private locationUpdateCallbacks: Set<(location: Location.LocationObject) => void> = new Set();

  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  }

  async getCurrentLocation(): Promise<Location.LocationObject | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // 10 seconds
      });

      this.lastKnownLocation = location;
      return location;
    } catch (error) {
      console.error('Error getting current location:', error);
      return this.lastKnownLocation;
    }
  }

  /**
   * Get last known location without requesting new position
   */
  getLastKnownLocation(): Location.LocationObject | null {
    return this.lastKnownLocation;
  }

  async startLocationUpdates(callback: (location: Location.LocationObject) => void): Promise<boolean> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return false;
      }

      // Add callback to set
      this.locationUpdateCallbacks.add(callback);

      // Start watching if not already started
      if (!this.watchId) {
        this.watchId = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: API_CONFIG.LOCATION.UPDATE_INTERVAL,
            distanceInterval: API_CONFIG.LOCATION.ACCURACY_THRESHOLD,
          },
          (location) => {
            this.lastKnownLocation = location;
            this.notifyLocationUpdate(location);
          }
        );
      }

      return true;
    } catch (error) {
      console.error('Error starting location updates:', error);
      return false;
    }
  }

  /**
   * Remove a specific location update callback
   */
  removeLocationCallback(callback: (location: Location.LocationObject) => void): void {
    this.locationUpdateCallbacks.delete(callback);
  }

  /**
   * Notify all callbacks of location updates
   */
  private notifyLocationUpdate(location: Location.LocationObject): void {
    this.locationUpdateCallbacks.forEach(callback => {
      try {
        callback(location);
      } catch (error) {
        console.error('Error in location callback:', error);
      }
    });
  }

  stopLocationUpdates(): void {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
    this.locationUpdateCallbacks.clear();
  }

  async reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
    // Force mock mode for location service (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Mock: Reverse geocoding for', latitude.toFixed(4), longitude.toFixed(4));
      
      // Return realistic London addresses based on coordinates
      const mockAddresses = [
        '123 Oxford Street, London, UK',
        '456 Liverpool Street, London, UK',
        '789 Westminster Bridge Road, London, UK',
        '321 Regent Street, London, UK',
        '654 Tottenham Court Road, London, UK',
        'London Heathrow Airport (LHR), UK'
      ];
      
      // Use coordinates as seed for consistent results
      const index = Math.floor((latitude + longitude) * 1000) % mockAddresses.length;
      return mockAddresses[Math.abs(index)];
    }

    try {
      // Try API first, fallback to Expo Location
      const apiResult = await this.reverseGeocodeAPI({ latitude, longitude, address: '' });
      if (apiResult && apiResult.results.length > 0) {
        return apiResult.results[0].formattedAddress;
      }
      
      // Fallback to Expo Location
      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (result.length > 0) {
        const address = result[0];
        return `${address.name || ''} ${address.street || ''}, ${address.city || ''}, ${address.region || ''}`.trim();
      }

      return null;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return null;
    }
  }

  /**
   * Get navigation route between two points
   */
  async getRoute(from: LocationType, to: LocationType, optimize: boolean = true): Promise<NavigationRoute> {
    try {
      const response: ApiResponse<NavigationRoute> = await apiService.post(
        API_CONFIG.ENDPOINTS.NAVIGATION.ROUTE,
        {
          from,
          to,
          optimize,
          avoidTolls: false,
          avoidHighways: false,
        },
        {
          cacheKey: `route-${from.latitude}-${from.longitude}-${to.latitude}-${to.longitude}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get route');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error getting route');
    }
  }

  /**
   * Get ETA between two points
   */
  async getETA(request: ETARequest): Promise<ETAResponse> {
    try {
      const response: ApiResponse<ETAResponse> = await apiService.post(
        API_CONFIG.ENDPOINTS.NAVIGATION.ETA,
        request,
        {
          cacheKey: `eta-${request.from.latitude}-${request.from.longitude}-${request.to.latitude}-${request.to.longitude}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.SHORT,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get ETA');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error getting ETA');
    }
  }

  /**
   * Geocode an address to coordinates
   */
  async geocode(request: GeocodingRequest): Promise<GeocodingResponse> {
    try {
      const response: ApiResponse<GeocodingResponse> = await apiService.get(
        '/location/geocode',
        {
          params: request,
          cacheKey: `geocode-${request.address}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.LONG,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to geocode address');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error geocoding address');
    }
  }

  /**
   * Reverse geocode coordinates to address
   */
  async reverseGeocodeAPI(location: LocationType): Promise<ReverseGeocodingResponse> {
    try {
      const response: ApiResponse<ReverseGeocodingResponse> = await apiService.get(
        '/location/reverse-geocode',
        {
          params: {
            latitude: location.latitude,
            longitude: location.longitude,
          },
          cacheKey: `reverse-geocode-${location.latitude}-${location.longitude}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.LONG,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to reverse geocode');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error reverse geocoding');
    }
  }

  /**
   * Search for places/addresses
   */
  async searchPlaces(query: string, location?: LocationType, radius?: number): Promise<GeocodingResponse> {
    try {
      const response: ApiResponse<GeocodingResponse> = await apiService.get(
        '/location/search',
        {
          params: {
            query,
            latitude: location?.latitude,
            longitude: location?.longitude,
            radius,
          },
          cacheKey: `place-search-${query}-${location?.latitude}-${location?.longitude}`,
          cacheDuration: API_CONFIG.CACHE_DURATION.MEDIUM,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to search places');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Network error searching places');
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  static calculateDistance(from: LocationType, to: LocationType): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = from.latitude * Math.PI / 180;
    const φ2 = to.latitude * Math.PI / 180;
    const Δφ = (to.latitude - from.latitude) * Math.PI / 180;
    const Δλ = (to.longitude - from.longitude) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  }

  /**
   * Calculate bearing between two points
   */
  static calculateBearing(from: LocationType, to: LocationType): number {
    const φ1 = from.latitude * Math.PI / 180;
    const φ2 = to.latitude * Math.PI / 180;
    const Δλ = (to.longitude - from.longitude) * Math.PI / 180;

    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360; // Normalize to 0-360 degrees
  }

  /**
   * Check if a point is within a radius of another point
   */
  static isWithinRadius(center: LocationType, point: LocationType, radiusMeters: number): boolean {
    const distance = this.calculateDistance(center, point);
    return distance <= radiusMeters;
  }

  /**
   * Format duration in seconds to human readable string
   */
  static formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      const minutes = Math.round(seconds / 60);
      return `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.round((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  }

  /**
   * Format distance in meters to human readable string
   */
  static formatDistance(meters: number): string {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      const km = meters / 1000;
      return `${km.toFixed(1)}km`;
    }
  }

  /**
   * Validate coordinates
   */
  static isValidCoordinates(latitude: number, longitude: number): boolean {
    return (
      latitude >= -90 && latitude <= 90 &&
      longitude >= -180 && longitude <= 180 &&
      !isNaN(latitude) && !isNaN(longitude)
    );
  }

  /**
   * Convert LocationObject to LocationType
   */
  static convertToLocationType(locationObject: Location.LocationObject, address?: string): LocationType {
    return {
      latitude: locationObject.coords.latitude,
      longitude: locationObject.coords.longitude,
      address: address || `${locationObject.coords.latitude}, ${locationObject.coords.longitude}`,
    };
  }
}

export default LocationService.getInstance();