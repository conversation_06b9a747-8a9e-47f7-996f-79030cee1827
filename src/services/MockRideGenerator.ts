import { mockData } from '../data/mockData';
import { Ride } from '../types';
import { API_CONFIG } from '../config/api.config';

class MockRideGenerator {
  private static instance: MockRideGenerator;
  private rideGenerationInterval: NodeJS.Timeout | null = null;
  private isGenerating = false;
  private callbacks: Set<(ride: Ride) => void> = new Set();

  public static getInstance(): MockRideGenerator {
    if (!MockRideGenerator.instance) {
      MockRideGenerator.instance = new MockRideGenerator();
    }
    return MockRideGenerator.instance;
  }

  /**
   * Start generating mock ride requests automatically
   */
  startGeneratingRides(): void {
    if (this.rideGenerationInterval || !(API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE)) {
      return;
    }

    this.isGenerating = true;
    console.log('🚗 Starting automatic mock ride generation...');

    // Generate a ride every 30-90 seconds
    const generateRide = () => {
      if (!this.isGenerating) return;

      const ride = mockData.generateRandomRideRequest();
      console.log('🎭 New mock ride request generated:', ride.id);
      
      // Notify all subscribers
      this.callbacks.forEach(callback => {
        try {
          callback(ride);
        } catch (error) {
          console.error('Error in ride generation callback:', error);
        }
      });

      // Schedule next ride
      const nextInterval = 30000 + Math.random() * 60000; // 30-90 seconds
      this.rideGenerationInterval = setTimeout(generateRide, nextInterval);
    };

    // Start with first ride after a short delay
    this.rideGenerationInterval = setTimeout(generateRide, 5000);
  }

  /**
   * Stop generating mock ride requests
   */
  stopGeneratingRides(): void {
    this.isGenerating = false;
    
    if (this.rideGenerationInterval) {
      clearTimeout(this.rideGenerationInterval);
      this.rideGenerationInterval = null;
    }
    
    console.log('🛑 Stopped automatic mock ride generation');
  }

  /**
   * Subscribe to new ride notifications
   */
  onNewRide(callback: (ride: Ride) => void): () => void {
    this.callbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * Generate a single mock ride immediately
   */
  generateSingleRide(): Ride {
    const ride = mockData.generateRandomRideRequest();
    console.log('🎭 Single mock ride generated:', ride.id);
    
    // Notify subscribers
    this.callbacks.forEach(callback => {
      try {
        callback(ride);
      } catch (error) {
        console.error('Error in ride generation callback:', error);
      }
    });
    
    return ride;
  }

  /**
   * Check if ride generation is active
   */
  isActive(): boolean {
    return this.isGenerating;
  }

  /**
   * Get number of active subscribers
   */
  getSubscriberCount(): number {
    return this.callbacks.size;
  }
}

// Export singleton instance
export const mockRideGenerator = MockRideGenerator.getInstance();
export default MockRideGenerator;