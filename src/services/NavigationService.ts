import * as Location from 'expo-location';
import * as Speech from 'expo-speech';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface NavigationStep {
  instruction: string;
  distance: string;
  duration: string;
  maneuver: string;
  location: {
    latitude: number;
    longitude: number;
  };
}

export interface NavigationRoute {
  steps: NavigationStep[];
  totalDistance: string;
  totalDuration: string;
  polyline: string;
}

export class NavigationService {
  private static instance: NavigationService;
  private currentRoute: NavigationRoute | null = null;
  private currentStepIndex: number = 0;
  private isNavigating: boolean = false;
  private locationSubscription: Location.LocationSubscription | null = null;
  private readonly API_KEY = 'AIzaSyBQyfTOFI2Rn28Gqy2CjeZ8IdxznKhsSQQ';

  public static getInstance(): NavigationService {
    if (!NavigationService.instance) {
      NavigationService.instance = new NavigationService();
    }
    return NavigationService.instance;
  }

  // Get detailed directions with turn-by-turn instructions
  async getDetailedDirections(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number }
  ): Promise<NavigationRoute | null> {
    try {
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${this.API_KEY}&alternatives=false&units=metric`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.error_message) {
        console.error('Google Maps API Error:', data.error_message);
        throw new Error(`API Error: ${data.error_message}`);
      }
      
      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        const leg = route.legs[0];
        
        const steps: NavigationStep[] = leg.steps.map((step: any) => ({
          instruction: this.cleanInstruction(step.html_instructions),
          distance: step.distance.text,
          duration: step.duration.text,
          maneuver: step.maneuver || 'straight',
          location: {
            latitude: step.end_location.lat,
            longitude: step.end_location.lng,
          },
        }));

        this.currentRoute = {
          steps,
          totalDistance: leg.distance.text,
          totalDuration: leg.duration.text,
          polyline: route.overview_polyline.points,
        };

        return this.currentRoute;
      }
      return null;
    } catch (error) {
      console.error('Error getting detailed directions:', error);
      return null;
    }
  }

  // Start turn-by-turn navigation
  async startTurnByTurnNavigationInternal(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number },
    onStepUpdate?: (step: NavigationStep, stepIndex: number) => void,
    onArrival?: () => void
  ): Promise<boolean> {
    try {
      // Get permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission denied');
      }

      // Get route
      const route = await this.getDetailedDirections(origin, destination);
      if (!route) {
        throw new Error('Could not get route');
      }

      this.isNavigating = true;
      this.currentStepIndex = 0;

      // Start location tracking
      this.locationSubscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
        },
        (location) => {
          this.handleLocationUpdate(location, onStepUpdate, onArrival);
        }
      );

      // Announce first instruction
      if (route.steps.length > 0) {
        this.announceInstruction(route.steps[0].instruction);
        onStepUpdate?.(route.steps[0], 0);
      }

      return true;
    } catch (error) {
      console.error('Error starting navigation:', error);
      return false;
    }
  }

  // Handle location updates during navigation
  private handleLocationUpdate(
    location: Location.LocationObject,
    onStepUpdate?: (step: NavigationStep, stepIndex: number) => void,
    onArrival?: () => void
  ) {
    if (!this.currentRoute || !this.isNavigating) return;

    const currentLocation = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
    };

    const currentStep = this.currentRoute.steps[this.currentStepIndex];
    if (!currentStep) return;

    // Calculate distance to next step
    const distanceToStep = this.calculateDistance(
      currentLocation,
      currentStep.location
    );

    // If within 50 meters of current step, move to next step
    if (distanceToStep < 50) {
      this.currentStepIndex++;
      
      if (this.currentStepIndex >= this.currentRoute.steps.length) {
        // Navigation complete
        this.stopNavigation();
        this.announceInstruction('You have arrived at your destination');
        onArrival?.();
      } else {
        // Move to next step
        const nextStep = this.currentRoute.steps[this.currentStepIndex];
        this.announceInstruction(nextStep.instruction);
        onStepUpdate?.(nextStep, this.currentStepIndex);
      }
    }
  }

  // Stop navigation
  stopNavigation() {
    this.isNavigating = false;
    this.currentStepIndex = 0;
    this.currentRoute = null;
    
    if (this.locationSubscription) {
      this.locationSubscription.remove();
      this.locationSubscription = null;
    }
  }

  // Announce instruction using text-to-speech
  private announceInstruction(instruction: string) {
    try {
      Speech.speak(instruction, {
        language: 'en-US',
        pitch: 1.0,
        rate: 0.9,
      });
    } catch (error) {
      console.log('Speech not available:', error);
    }
  }

  // Clean HTML instructions
  private cleanInstruction(htmlInstruction: string): string {
    return htmlInstruction
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .trim();
  }

  // Calculate distance between two points
  private calculateDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (point1.latitude * Math.PI) / 180;
    const φ2 = (point2.latitude * Math.PI) / 180;
    const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  // Get estimated time between two points
  async getEstimatedTime(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number }
  ): Promise<string> {
    try {
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${this.API_KEY}&units=metric`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.error_message) {
        console.error('Google Maps API Error:', data.error_message);
        throw new Error(`API Error: ${data.error_message}`);
      }
      
      if (data.routes && data.routes.length > 0) {
        return data.routes[0].legs[0].duration.text;
      }
      return '5 min';
    } catch (error) {
      console.error('Error getting estimated time:', error);
      return '5 min';
    }
  }

  // Get traffic-aware ETA with more detailed information
  async getTrafficAwareETA(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number }
  ): Promise<{
    duration: string;
    durationValue: number;
    distance: string;
    distanceValue: number;
    trafficDelay: number;
    arrivalTime: Date;
    averageSpeed?: number;
  } | null> {
    try {
      // Enhanced API call with more parameters for better accuracy
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${this.API_KEY}&departure_time=now&traffic_model=best_guess&units=metric&alternatives=false&mode=driving&optimize=true`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.error_message) {
        console.error('Google Maps API Error:', data.error_message);
        throw new Error(`API Error: ${data.error_message}`);
      }
      
      if (data.routes && data.routes.length > 0) {
        const leg = data.routes[0].legs[0];
        const normalDuration = leg.duration.value; // in seconds
        const trafficDuration = leg.duration_in_traffic?.value || normalDuration; // in seconds
        const distanceValue = leg.distance.value; // in meters
        
        // Calculate arrival time with traffic consideration
        const now = new Date();
        const arrivalTime = new Date(now.getTime() + (trafficDuration * 1000));
        
        // Calculate average speed in km/h based on traffic conditions
        // distance in meters / duration in seconds * 3.6 = km/h
        let averageSpeed: number | undefined;
        
        if (distanceValue > 0 && trafficDuration > 0) {
          // Calculate raw average speed
          const rawSpeed = (distanceValue / trafficDuration) * 3.6;
          
          // Apply some intelligence to the speed calculation:
          // 1. If we're on a very short segment (< 500m), speed might be unreliable
          if (distanceValue < 500) {
            // For very short segments, use a more conservative estimate
            // based on the road type if we can determine it from steps
            if (data.routes[0].legs[0].steps && data.routes[0].legs[0].steps.length > 0) {
              const step = data.routes[0].legs[0].steps[0];
              // Try to determine road type from HTML instructions
              const html = step.html_instructions || '';
              
              if (html.includes('highway') || html.includes('motorway') || html.includes('freeway')) {
                averageSpeed = Math.min(rawSpeed, 90); // Cap highway speed
              } else if (html.includes('avenue') || html.includes('road') || html.includes('street')) {
                averageSpeed = Math.min(rawSpeed, 50); // Cap urban road speed
              } else {
                averageSpeed = Math.min(rawSpeed, 30); // Cap local street speed
              }
            } else {
              // Without step info, use a reasonable default
              averageSpeed = Math.min(rawSpeed, 40);
            }
          } else {
            // For longer segments, the calculated speed is more reliable
            // but still cap it at reasonable values
            averageSpeed = Math.min(Math.round(rawSpeed), 130); // Cap at 130 km/h max
          }
        }
        
        return {
          duration: leg.duration_in_traffic?.text || leg.duration.text,
          durationValue: trafficDuration, // in seconds
          distance: leg.distance.text,
          distanceValue: distanceValue, // in meters
          trafficDelay: trafficDuration - normalDuration, // in seconds
          arrivalTime: arrivalTime,
          averageSpeed: averageSpeed // in km/h
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting traffic-aware ETA:', error);
      return null;
    }
  }

  // Start navigation using external app
  async startNavigation(
    destination: { latitude: number; longitude: number; address?: string },
    app: 'google' | 'apple' | 'waze'
  ): Promise<boolean> {
    try {
      const { Linking, Platform } = require('react-native');
      let url: string;

      switch (app) {
        case 'google':
          url = Platform.select({
            ios: `comgooglemaps://?daddr=${destination.latitude},${destination.longitude}&directionsmode=driving`,
            android: `google.navigation:q=${destination.latitude},${destination.longitude}&mode=d`
          });
          break;
        case 'apple':
          url = Platform.select({
            ios: `maps://?daddr=${destination.latitude},${destination.longitude}&dirflg=d`,
            android: `geo:${destination.latitude},${destination.longitude}?q=${destination.latitude},${destination.longitude}`
          });
          break;
        case 'waze':
          url = `waze://?ll=${destination.latitude},${destination.longitude}&navigate=yes`;
          break;
        default:
          return false;
      }

      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        return true;
      } else {
        // Fallback to web URL
        const webUrl = `https://maps.google.com/maps?daddr=${destination.latitude},${destination.longitude}&mode=driving`;
        await Linking.openURL(webUrl);
        return true;
      }
    } catch (error) {
      console.error('Error starting navigation:', error);
      return false;
    }
  }

  // Start turn-by-turn navigation (wrapper for existing method)
  async startTurnByTurnNavigation(
    destination: { latitude: number; longitude: number }
  ): Promise<boolean> {
    try {
      // Check location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.error('Location permission denied');
        throw new Error('Location permission is required for navigation');
      }

      // Check if location services are enabled
      const isLocationEnabled = await Location.hasServicesEnabledAsync();
      if (!isLocationEnabled) {
        console.error('Location services are disabled');
        throw new Error('Please enable location services');
      }

      // Get current location with timeout
      const currentLocation = await Promise.race([
        Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
          timeInterval: 10000,
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Location timeout')), 15000)
        )
      ]) as Location.LocationObject;

      const origin = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude
      };

      // Validate coordinates
      if (!this.isValidCoordinate(origin) || !this.isValidCoordinate(destination)) {
        throw new Error('Invalid coordinates provided');
      }

      return await this.startTurnByTurnNavigationInternal(origin, destination);
    } catch (error) {
      console.error('Error starting turn-by-turn navigation:', error);
      return false;
    }
  }

  // Validate coordinate object
  private isValidCoordinate(coord: { latitude: number; longitude: number }): boolean {
    return (
      typeof coord.latitude === 'number' &&
      typeof coord.longitude === 'number' &&
      coord.latitude >= -90 && coord.latitude <= 90 &&
      coord.longitude >= -180 && coord.longitude <= 180 &&
      !isNaN(coord.latitude) && !isNaN(coord.longitude)
    );
  }

  // Get current speed estimate based on Google's traffic data
  async getCurrentSpeedEstimate(
    currentLocation: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number },
    previousLocation?: { latitude: number; longitude: number },
    previousTimestamp?: number
  ): Promise<number | null> {
    try {
      // Method 1: Use Google's traffic data to estimate speed - most accurate
      // We'll use a shorter segment for more accurate current speed
      // Create a waypoint that's a short distance ahead in the direction of travel
      let waypointLocation = currentLocation;
      
      // If we have previous location, we can determine direction of travel
      if (previousLocation && previousTimestamp) {
        const currentTimestamp = new Date().getTime();
        const timeDiff = (currentTimestamp - previousTimestamp) / 1000; // in seconds
        
        // Only use direction if the readings are recent (within 10 seconds)
        if (timeDiff < 10) {
          // Calculate direction vector
          const latDiff = currentLocation.latitude - previousLocation.latitude;
          const lngDiff = currentLocation.longitude - previousLocation.longitude;
          
          // Create a waypoint 500 meters ahead in the direction of travel
          // This gives us a more accurate current segment speed rather than average route speed
          const distance = 0.005; // Roughly 500m in decimal degrees
          const magnitude = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
          
          if (magnitude > 0) {
            waypointLocation = {
              latitude: currentLocation.latitude + (latDiff / magnitude) * distance,
              longitude: currentLocation.longitude + (lngDiff / magnitude) * distance
            };
          }
        }
      }
      
      // Get ETA to the waypoint (short segment ahead) for current speed
      const shortSegmentEta = await this.getTrafficAwareETA(currentLocation, waypointLocation);
      if (shortSegmentEta?.averageSpeed) {
        return shortSegmentEta.averageSpeed;
      }
      
      // Fallback to full route average speed
      const fullRouteEta = await this.getTrafficAwareETA(currentLocation, destination);
      if (fullRouteEta?.averageSpeed) {
        return fullRouteEta.averageSpeed;
      }
      
      // Method 2: Calculate speed from position changes as fallback
      if (previousLocation && previousTimestamp) {
        const currentTimestamp = new Date().getTime();
        const timeDiff = (currentTimestamp - previousTimestamp) / 1000; // in seconds
        
        if (timeDiff > 0) {
          // Calculate distance in meters
          const distance = this.calculateDistance(previousLocation, currentLocation);
          
          // Calculate speed in m/s and convert to km/h
          const speedKmh = (distance / timeDiff) * 3.6;
          
          // Apply a threshold to filter out small movements when stationary
          // and cap at reasonable maximum (200 km/h) to filter out GPS jumps
          if (speedKmh < 1) return 0;
          return Math.min(Math.round(speedKmh), 200);
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting current speed estimate:', error);
      return null;
    }
  }

  // Get current navigation state
  getCurrentState() {
    return {
      isNavigating: this.isNavigating,
      currentRoute: this.currentRoute,
      currentStepIndex: this.currentStepIndex,
      currentStep: this.currentRoute?.steps[this.currentStepIndex] || null,
    };
  }
}