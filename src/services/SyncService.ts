import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from './ApiService';
import { API_CONFIG } from '../config/api.config';
import NetInfo from '@react-native-community/netinfo';
import { PendingAction } from '../types';

// Sync service types
export interface SyncableAction extends PendingAction {
  priority: 'low' | 'medium' | 'high' | 'critical';
  dependencies?: string[]; // Action IDs that must complete first
  maxRetries: number;
  expiry?: Date; // When this action becomes invalid
}

export interface SyncResult {
  success: boolean;
  actionId: string;
  error?: string;
  data?: any;
}

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  pendingCount: number;
  lastSyncTime: Date | null;
  failedCount: number;
  queuedActions: SyncableAction[];
}

export interface SyncConfiguration {
  batchSize: number;
  retryDelay: number;
  maxConcurrentRequests: number;
  priorityOrder: ('critical' | 'high' | 'medium' | 'low')[];
  offlineStorageLimit: number; // Max number of actions to store offline
}

// Storage keys
const PENDING_ACTIONS_KEY = 'sync_pending_actions';
const FAILED_ACTIONS_KEY = 'sync_failed_actions';
const SYNC_STATUS_KEY = 'sync_status';
const OFFLINE_DATA_KEY = 'sync_offline_data';

class SyncService {
  private static instance: SyncService;
  private isOnline = true;
  private isSyncing = false;
  private pendingActions: SyncableAction[] = [];
  private failedActions: SyncableAction[] = [];
  private syncIntervalId: NodeJS.Timeout | null = null;
  private offlineData = new Map<string, { data: any; timestamp: number; expiry?: number }>();
  
  private config: SyncConfiguration = {
    batchSize: 5,
    retryDelay: 2000,
    maxConcurrentRequests: 3,
    priorityOrder: ['critical', 'high', 'medium', 'low'],
    offlineStorageLimit: 100,
  };

  private syncListeners = new Set<(status: SyncStatus) => void>();

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  /**
   * Initialize sync service
   */
  async initialize(): Promise<void> {
    try {
      // Load pending actions from storage
      await this.loadPendingActions();
      
      // Load offline data
      await this.loadOfflineData();
      
      // Set up network state monitoring
      this.setupNetworkMonitoring();
      
      // Start periodic sync
      this.startPeriodicSync();
      
      console.log('SyncService initialized');
    } catch (error) {
      console.error('Failed to initialize SyncService:', error);
    }
  }

  /**
   * Add action to sync queue
   */
  async queueAction(action: Omit<SyncableAction, 'id' | 'timestamp' | 'retryCount'>): Promise<string> {
    const syncableAction: SyncableAction = {
      ...action,
      id: this.generateActionId(),
      timestamp: new Date(),
      retryCount: 0,
    };

    this.pendingActions.push(syncableAction);
    await this.savePendingActions();
    
    // Try immediate sync if online
    if (this.isOnline && !this.isSyncing) {
      this.syncPendingActions();
    }

    this.notifyListeners();
    return syncableAction.id;
  }

  /**
   * Store data for offline access
   */
  async storeOfflineData(key: string, data: any, expiryMinutes?: number): Promise<void> {
    const expiry = expiryMinutes ? Date.now() + (expiryMinutes * 60 * 1000) : undefined;
    
    this.offlineData.set(key, {
      data,
      timestamp: Date.now(),
      expiry,
    });

    await this.saveOfflineData();
  }

  /**
   * Retrieve offline data
   */
  getOfflineData<T>(key: string): T | null {
    const stored = this.offlineData.get(key);
    
    if (!stored) {
      return null;
    }

    // Check expiry
    if (stored.expiry && Date.now() > stored.expiry) {
      this.offlineData.delete(key);
      this.saveOfflineData();
      return null;
    }

    return stored.data as T;
  }

  /**
   * Check if data exists offline
   */
  hasOfflineData(key: string): boolean {
    return this.getOfflineData(key) !== null;
  }

  /**
   * Clear offline data
   */
  async clearOfflineData(key?: string): Promise<void> {
    if (key) {
      this.offlineData.delete(key);
    } else {
      this.offlineData.clear();
    }
    
    await this.saveOfflineData();
  }

  /**
   * Sync pending actions
   */
  private async syncPendingActions(): Promise<void> {
    if (this.isSyncing || !this.isOnline || this.pendingActions.length === 0) {
      return;
    }

    this.isSyncing = true;
    this.notifyListeners();

    try {
      // Sort actions by priority and dependencies
      const sortedActions = this.sortActionsByPriority();
      
      // Process actions in batches
      const batches = this.createBatches(sortedActions);
      
      for (const batch of batches) {
        if (!this.isOnline) {
          break; // Stop if we go offline
        }
        
        await this.processBatch(batch);
      }

      // Clean up completed actions
      await this.savePendingActions();
      
    } catch (error) {
      console.error('Error during sync:', error);
    } finally {
      this.isSyncing = false;
      this.notifyListeners();
    }
  }

  /**
   * Sort actions by priority and dependencies
   */
  private sortActionsByPriority(): SyncableAction[] {
    const sorted = [...this.pendingActions];
    
    // Remove expired actions
    const now = new Date();
    const validActions = sorted.filter(action => !action.expiry || action.expiry > now);
    
    // Sort by priority
    return validActions.sort((a, b) => {
      const aPriorityIndex = this.config.priorityOrder.indexOf(a.priority);
      const bPriorityIndex = this.config.priorityOrder.indexOf(b.priority);
      
      if (aPriorityIndex !== bPriorityIndex) {
        return aPriorityIndex - bPriorityIndex;
      }
      
      // Secondary sort by timestamp (older first)
      return a.timestamp.getTime() - b.timestamp.getTime();
    });
  }

  /**
   * Create batches for processing
   */
  private createBatches(actions: SyncableAction[]): SyncableAction[][] {
    const batches: SyncableAction[][] = [];
    const batchSize = this.config.batchSize;
    
    for (let i = 0; i < actions.length; i += batchSize) {
      batches.push(actions.slice(i, i + batchSize));
    }
    
    return batches;
  }

  /**
   * Process a batch of actions
   */
  private async processBatch(batch: SyncableAction[]): Promise<void> {
    const promises = batch.map(action => this.processAction(action));
    
    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Error processing batch:', error);
    }
  }

  /**
   * Process individual action
   */
  private async processAction(action: SyncableAction): Promise<SyncResult> {
    try {
      // Check dependencies
      if (action.dependencies && action.dependencies.length > 0) {
        const dependenciesMet = this.checkDependencies(action.dependencies);
        if (!dependenciesMet) {
          return { success: false, actionId: action.id, error: 'Dependencies not met' };
        }
      }

      // Execute the action
      const result = await this.executeAction(action);
      
      if (result.success) {
        // Remove from pending actions
        this.pendingActions = this.pendingActions.filter(a => a.id !== action.id);
        
        // Remove from failed actions if it was there
        this.failedActions = this.failedActions.filter(a => a.id !== action.id);
        
        return result;
      } else {
        // Handle failure
        return this.handleActionFailure(action, result.error || 'Unknown error');
      }
      
    } catch (error: any) {
      return this.handleActionFailure(action, error.message || 'Network error');
    }
  }

  /**
   * Execute action based on its type
   */
  private async executeAction(action: SyncableAction): Promise<SyncResult> {
    try {
      let result: any;
      
      switch (action.type) {
        case 'driver_location_update':
          result = await apiService.put('/driver/location', action.payload);
          break;
          
        case 'ride_status_update':
          result = await apiService.put(`/rides/${action.payload.rideId}/status`, action.payload);
          break;
          
        case 'trip_complete':
          result = await apiService.post('/trips/complete', action.payload);
          break;
          
        case 'driver_status_update':
          result = await apiService.put('/driver/status', action.payload);
          break;
          
        case 'ride_accept':
          result = await apiService.post(`/rides/${action.payload.rideId}/accept`, action.payload);
          break;
          
        case 'ride_reject':
          result = await apiService.post(`/rides/${action.payload.rideId}/reject`, action.payload);
          break;
          
        default:
          throw new Error(`Unknown action type: ${action.type}`);
      }

      return {
        success: true,
        actionId: action.id,
        data: result.data,
      };
      
    } catch (error: any) {
      return {
        success: false,
        actionId: action.id,
        error: error.message,
      };
    }
  }

  /**
   * Handle action failure
   */
  private async handleActionFailure(action: SyncableAction, error: string): Promise<SyncResult> {
    action.retryCount++;
    
    if (action.retryCount >= action.maxRetries) {
      // Move to failed actions
      this.failedActions.push(action);
      this.pendingActions = this.pendingActions.filter(a => a.id !== action.id);
      
      await this.saveFailedActions();
      
      return {
        success: false,
        actionId: action.id,
        error: `Max retries exceeded: ${error}`,
      };
    } else {
      // Keep in pending for retry
      return {
        success: false,
        actionId: action.id,
        error: `Retry ${action.retryCount}/${action.maxRetries}: ${error}`,
      };
    }
  }

  /**
   * Check if dependencies are met
   */
  private checkDependencies(dependencies: string[]): boolean {
    // Check if all dependency actions have been completed (not in pending or failed)
    return dependencies.every(depId => 
      !this.pendingActions.some(a => a.id === depId) &&
      !this.failedActions.some(a => a.id === depId)
    );
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (!wasOnline && this.isOnline) {
        // Just came back online - sync pending actions
        console.log('Network restored, syncing pending actions');
        this.syncPendingActions();
      } else if (wasOnline && !this.isOnline) {
        console.log('Network lost, entering offline mode');
      }
      
      this.notifyListeners();
    });
  }

  /**
   * Start periodic sync
   */
  private startPeriodicSync(): void {
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
    }
    
    // Sync every 30 seconds when online
    this.syncIntervalId = setInterval(() => {
      if (this.isOnline && this.pendingActions.length > 0) {
        this.syncPendingActions();
      }
    }, 30000);
  }

  /**
   * Load pending actions from storage
   */
  private async loadPendingActions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(PENDING_ACTIONS_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.pendingActions = parsed.map((action: any) => ({
          ...action,
          timestamp: new Date(action.timestamp),
          expiry: action.expiry ? new Date(action.expiry) : undefined,
        }));
      }
      
      const failedStored = await AsyncStorage.getItem(FAILED_ACTIONS_KEY);
      if (failedStored) {
        const parsed = JSON.parse(failedStored);
        this.failedActions = parsed.map((action: any) => ({
          ...action,
          timestamp: new Date(action.timestamp),
          expiry: action.expiry ? new Date(action.expiry) : undefined,
        }));
      }
    } catch (error) {
      console.error('Error loading pending actions:', error);
    }
  }

  /**
   * Save pending actions to storage
   */
  private async savePendingActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(PENDING_ACTIONS_KEY, JSON.stringify(this.pendingActions));
    } catch (error) {
      console.error('Error saving pending actions:', error);
    }
  }

  /**
   * Save failed actions to storage
   */
  private async saveFailedActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(FAILED_ACTIONS_KEY, JSON.stringify(this.failedActions));
    } catch (error) {
      console.error('Error saving failed actions:', error);
    }
  }

  /**
   * Load offline data from storage
   */
  private async loadOfflineData(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(OFFLINE_DATA_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.offlineData = new Map(Object.entries(parsed));
      }
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  }

  /**
   * Save offline data to storage
   */
  private async saveOfflineData(): Promise<void> {
    try {
      const dataObject = Object.fromEntries(this.offlineData);
      await AsyncStorage.setItem(OFFLINE_DATA_KEY, JSON.stringify(dataObject));
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  }

  /**
   * Subscribe to sync status updates
   */
  onStatusChange(callback: (status: SyncStatus) => void): () => void {
    this.syncListeners.add(callback);
    
    // Send current status immediately
    callback(this.getStatus());
    
    return () => {
      this.syncListeners.delete(callback);
    };
  }

  /**
   * Get current sync status
   */
  getStatus(): SyncStatus {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      pendingCount: this.pendingActions.length,
      lastSyncTime: null, // TODO: Track last sync time
      failedCount: this.failedActions.length,
      queuedActions: [...this.pendingActions],
    };
  }

  /**
   * Notify status listeners
   */
  private notifyListeners(): void {
    const status = this.getStatus();
    this.syncListeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('Error in sync status listener:', error);
      }
    });
  }

  /**
   * Force sync now
   */
  async forcSync(): Promise<void> {
    if (!this.isOnline) {
      throw new Error('Cannot sync while offline');
    }
    
    await this.syncPendingActions();
  }

  /**
   * Clear all pending actions
   */
  async clearAllPendingActions(): Promise<void> {
    this.pendingActions = [];
    this.failedActions = [];
    await this.savePendingActions();
    await this.saveFailedActions();
    this.notifyListeners();
  }

  /**
   * Retry failed actions
   */
  async retryFailedActions(): Promise<void> {
    // Move failed actions back to pending with reset retry count
    this.failedActions.forEach(action => {
      action.retryCount = 0;
      this.pendingActions.push(action);
    });
    
    this.failedActions = [];
    await this.savePendingActions();
    await this.saveFailedActions();
    
    // Try to sync
    if (this.isOnline) {
      await this.syncPendingActions();
    }
  }

  /**
   * Update sync configuration
   */
  updateConfig(config: Partial<SyncConfiguration>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Generate unique action ID
   */
  private generateActionId(): string {
    return `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup expired data and actions
   */
  async cleanup(): Promise<void> {
    const now = Date.now();
    
    // Clean up expired offline data
    for (const [key, data] of this.offlineData.entries()) {
      if (data.expiry && now > data.expiry) {
        this.offlineData.delete(key);
      }
    }
    
    // Clean up expired actions
    this.pendingActions = this.pendingActions.filter(action => 
      !action.expiry || action.expiry.getTime() > now
    );
    
    this.failedActions = this.failedActions.filter(action => 
      !action.expiry || action.expiry.getTime() > now
    );
    
    // Save changes
    await Promise.all([
      this.saveOfflineData(),
      this.savePendingActions(),
      this.saveFailedActions(),
    ]);
    
    this.notifyListeners();
  }

  /**
   * Destroy sync service
   */
  destroy(): void {
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
      this.syncIntervalId = null;
    }
    
    this.syncListeners.clear();
    this.pendingActions = [];
    this.failedActions = [];
    this.offlineData.clear();
  }
}

// Export singleton instance
export const syncService = SyncService.getInstance();
export default SyncService;