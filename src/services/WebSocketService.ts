import { API_CONFIG } from '../config/api.config';
import { apiService } from './ApiService';
import { Ride, Driver, Location } from '../types';

// WebSocket event types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: Date;
  id?: string;
}

export interface RideUpdateMessage extends WebSocketMessage {
  type: 'ride_update';
  data: {
    ride: Ride;
    changeType: 'created' | 'updated' | 'cancelled' | 'completed';
  };
}

export interface BookingUpdateMessage extends WebSocketMessage {
  type: 'booking_update';
  data: {
    booking: Ride;
    changeType: 'new' | 'accepted' | 'rejected' | 'cancelled';
    targetDriverId?: string;
  };
}

export interface DriverLocationMessage extends WebSocketMessage {
  type: 'driver_location';
  data: {
    driverId: string;
    location: Location;
    heading?: number;
    speed?: number;
  };
}

export interface ChatMessage extends WebSocketMessage {
  type: 'chat_message';
  data: {
    fromUserId: string;
    toUserId: string;
    message: string;
    messageType: 'text' | 'audio' | 'image' | 'location';
    rideId?: string;
    metadata?: Record<string, any>;
  };
}

export interface SystemMessage extends WebSocketMessage {
  type: 'system_message';
  data: {
    priority: 'low' | 'medium' | 'high' | 'urgent';
    title: string;
    message: string;
    actionRequired?: boolean;
    actionUrl?: string;
  };
}

export interface ConnectionStatus {
  connected: boolean;
  reconnecting: boolean;
  lastConnected: Date | null;
  reconnectAttempts: number;
  error: string | null;
}

type EventCallback<T extends WebSocketMessage = WebSocketMessage> = (message: T) => void;
type ConnectionCallback = (status: ConnectionStatus) => void;

class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private connectionStatus: ConnectionStatus = {
    connected: false,
    reconnecting: false,
    lastConnected: null,
    reconnectAttempts: 0,
    error: null,
  };
  
  private eventListeners = new Map<string, Set<EventCallback>>();
  private connectionListeners = new Set<ConnectionCallback>();
  private messageQueue: WebSocketMessage[] = [];
  private reconnectTimeoutId: NodeJS.Timeout | null = null;
  private heartbeatIntervalId: NodeJS.Timeout | null = null;
  private driverId: string | null = null;

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * Connect to WebSocket server
   */
  async connect(driverId: string): Promise<void> {
    // Force mock mode for WebSocket (only auth uses real API)
    if (API_CONFIG.FORCE_MOCK_NON_AUTH || API_CONFIG.FORCE_MOCK_MODE || API_CONFIG.DISABLE_API_CALLS) {
      console.log('🎭 Mock: Skipping WebSocket connection in mock mode');
      this.driverId = driverId;
      this.updateConnectionStatus({ 
        connected: true, 
        reconnecting: false, 
        error: null,
        reconnectAttempts: 0 
      });
      return;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    this.driverId = driverId;
    
    try {
      // Get auth token for WebSocket connection
      const isAuthenticated = await apiService.isAuthenticated();
      if (!isAuthenticated) {
        throw new Error('Not authenticated');
      }

      this.updateConnectionStatus({ reconnecting: true, error: null });

      const wsUrl = `${API_CONFIG.WS_BASE_URL}?driverId=${driverId}`;
      this.ws = new WebSocket(wsUrl);

      this.setupEventListeners();
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.onConnectionOpen();
          resolve();
        };

        this.ws!.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });

    } catch (error: any) {
      this.updateConnectionStatus({ 
        connected: false, 
        reconnecting: false, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.clearReconnectTimeout();
    this.clearHeartbeatInterval();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }

    this.updateConnectionStatus({ 
      connected: false, 
      reconnecting: false,
      reconnectAttempts: 0,
      error: null 
    });
  }

  /**
   * Send message to WebSocket server
   */
  send(message: Omit<WebSocketMessage, 'timestamp' | 'id'>): void {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: new Date(),
      id: this.generateMessageId(),
    };

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(fullMessage));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        this.queueMessage(fullMessage);
      }
    } else {
      this.queueMessage(fullMessage);
      
      // Try to reconnect if not already doing so
      if (!this.connectionStatus.reconnecting && this.driverId) {
        this.attemptReconnect();
      }
    }
  }

  /**
   * Subscribe to WebSocket events
   */
  on<T extends WebSocketMessage = WebSocketMessage>(
    eventType: string, 
    callback: EventCallback<T>
  ): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    
    const listeners = this.eventListeners.get(eventType)!;
    listeners.add(callback as EventCallback);

    // Return unsubscribe function
    return () => {
      listeners.delete(callback as EventCallback);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    };
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionChange(callback: ConnectionCallback): () => void {
    this.connectionListeners.add(callback);

    // Return unsubscribe function
    return () => {
      this.connectionListeners.delete(callback);
    };
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * Send location update
   */
  sendLocationUpdate(location: Location, heading?: number, speed?: number): void {
    this.send({
      type: 'location_update',
      data: {
        driverId: this.driverId,
        location,
        heading,
        speed,
      },
    });
  }

  /**
   * Send ride status update
   */
  sendRideStatusUpdate(
    rideId: string, 
    status: string, 
    location: Location, 
    metadata?: Record<string, any>
  ): void {
    this.send({
      type: 'ride_status_update',
      data: {
        rideId,
        driverId: this.driverId,
        status,
        location,
        metadata,
      },
    });
  }

  /**
   * Send chat message
   */
  sendChatMessage(
    toUserId: string, 
    message: string, 
    messageType: 'text' | 'audio' | 'image' | 'location' = 'text',
    rideId?: string,
    metadata?: Record<string, any>
  ): void {
    this.send({
      type: 'chat_message',
      data: {
        fromUserId: this.driverId!,
        toUserId,
        message,
        messageType,
        rideId,
        metadata,
      },
    });
  }

  /**
   * Join a room (for grouped messaging)
   */
  joinRoom(roomId: string): void {
    this.send({
      type: 'join_room',
      data: { roomId, driverId: this.driverId },
    });
  }

  /**
   * Leave a room
   */
  leaveRoom(roomId: string): void {
    this.send({
      type: 'leave_room',
      data: { roomId, driverId: this.driverId },
    });
  }

  /**
   * Setup WebSocket event listeners
   */
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.onConnectionOpen();
    };

    this.ws.onmessage = (event) => {
      this.onMessage(event);
    };

    this.ws.onclose = (event) => {
      this.onConnectionClose(event);
    };

    this.ws.onerror = (error) => {
      this.onConnectionError(error);
    };
  }

  /**
   * Handle WebSocket connection open
   */
  private onConnectionOpen(): void {
    console.log('WebSocket connected');
    
    this.updateConnectionStatus({
      connected: true,
      reconnecting: false,
      lastConnected: new Date(),
      reconnectAttempts: 0,
      error: null,
    });

    // Send queued messages
    this.flushMessageQueue();

    // Start heartbeat
    this.startHeartbeat();

    // Send driver online status
    this.send({
      type: 'driver_online',
      data: { driverId: this.driverId },
    });
  }

  /**
   * Handle WebSocket message
   */
  private onMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      if (API_CONFIG.isDevelopment) {
        console.log('WebSocket message received:', message.type, message.data);
      }

      // Handle system messages first
      if (message.type === 'pong') {
        // Heartbeat response - no action needed
        return;
      }

      // Emit to registered listeners
      const listeners = this.eventListeners.get(message.type);
      if (listeners) {
        listeners.forEach(callback => {
          try {
            callback(message);
          } catch (error) {
            console.error('Error in WebSocket event callback:', error);
          }
        });
      }

      // Also emit to 'all' listeners
      const allListeners = this.eventListeners.get('*');
      if (allListeners) {
        allListeners.forEach(callback => {
          try {
            callback(message);
          } catch (error) {
            console.error('Error in WebSocket all event callback:', error);
          }
        });
      }

    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket connection close
   */
  private onConnectionClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    
    this.clearHeartbeatInterval();
    
    this.updateConnectionStatus({
      connected: false,
      error: event.reason || `Connection closed (${event.code})`,
    });

    // Send driver offline status
    if (this.driverId) {
      // TODO: Send offline status via HTTP as WebSocket is closed
    }

    // Attempt to reconnect unless it was a manual disconnect
    if (event.code !== 1000 && this.driverId) {
      this.attemptReconnect();
    }
  }

  /**
   * Handle WebSocket connection error
   */
  private onConnectionError(error: Event): void {
    console.error('WebSocket error:', error);
    
    this.updateConnectionStatus({
      connected: false,
      error: 'Connection error',
    });
  }

  /**
   * Attempt to reconnect
   */
  private attemptReconnect(): void {
    if (this.connectionStatus.reconnecting) {
      return; // Already attempting to reconnect
    }

    if (this.connectionStatus.reconnectAttempts >= API_CONFIG.WEBSOCKET.RECONNECT_ATTEMPTS) {
      this.updateConnectionStatus({
        reconnecting: false,
        error: 'Maximum reconnect attempts exceeded',
      });
      return;
    }

    this.updateConnectionStatus({ reconnecting: true });

    const delay = Math.min(
      API_CONFIG.WEBSOCKET.RECONNECT_DELAY * Math.pow(2, this.connectionStatus.reconnectAttempts),
      30000 // Max 30 seconds
    );

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.connectionStatus.reconnectAttempts + 1})`);

    this.reconnectTimeoutId = setTimeout(async () => {
      try {
        this.updateConnectionStatus({ 
          reconnectAttempts: this.connectionStatus.reconnectAttempts + 1 
        });

        await this.connect(this.driverId!);
      } catch (error) {
        console.error('Reconnect attempt failed:', error);
        this.attemptReconnect(); // Try again
      }
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.clearHeartbeatInterval();
    
    this.heartbeatIntervalId = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping', data: {} });
      }
    }, API_CONFIG.WEBSOCKET.HEARTBEAT_INTERVAL);
  }

  /**
   * Clear heartbeat interval
   */
  private clearHeartbeatInterval(): void {
    if (this.heartbeatIntervalId) {
      clearInterval(this.heartbeatIntervalId);
      this.heartbeatIntervalId = null;
    }
  }

  /**
   * Clear reconnect timeout
   */
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }
  }

  /**
   * Queue message for sending when connection is restored
   */
  private queueMessage(message: WebSocketMessage): void {
    this.messageQueue.push(message);
    
    // Limit queue size to prevent memory issues
    if (this.messageQueue.length > 100) {
      this.messageQueue.shift(); // Remove oldest message
    }
  }

  /**
   * Send all queued messages
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!;
      
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        try {
          this.ws.send(JSON.stringify(message));
        } catch (error) {
          console.error('Failed to send queued message:', error);
          break; // Stop trying if we hit an error
        }
      } else {
        // Connection lost, put message back in queue
        this.messageQueue.unshift(message);
        break;
      }
    }
  }

  /**
   * Update connection status and notify listeners
   */
  private updateConnectionStatus(updates: Partial<ConnectionStatus>): void {
    this.connectionStatus = { ...this.connectionStatus, ...updates };
    
    this.connectionListeners.forEach(callback => {
      try {
        callback(this.connectionStatus);
      } catch (error) {
        console.error('Error in connection status callback:', error);
      }
    });
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.connectionStatus.connected && this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get WebSocket ready state
   */
  getReadyState(): number | null {
    return this.ws?.readyState || null;
  }
}

// Export singleton instance
export const webSocketService = WebSocketService.getInstance();
export default WebSocketService;