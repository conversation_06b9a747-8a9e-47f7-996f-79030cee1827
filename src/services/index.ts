// Service Registry and Dependency Injection
// This file provides a centralized way to access all services

import { apiService } from './ApiService';
import { authService } from './AuthService';
import { rideService } from './RideService';
import { driverService } from './DriverService';
import { tripService } from './TripService';
import { webSocketService } from './WebSocketService';
import { LocationService } from './LocationService';
import { syncService } from './SyncService';
import LocationServiceInstance from './LocationService';

// Service interface definitions for dependency injection
export interface ServiceRegistry {
  apiService: typeof apiService;
  authService: typeof authService;
  rideService: typeof rideService;
  driverService: typeof driverService;
  tripService: typeof tripService;
  webSocketService: typeof webSocketService;
  locationService: LocationService;
  syncService: typeof syncService;
}

// Service configuration interface
export interface ServiceConfig {
  apiBaseUrl?: string;
  wsBaseUrl?: string;
  enableOfflineMode?: boolean;
  enableWebSocket?: boolean;
  enableLocationTracking?: boolean;
}

class ServiceManager {
  private static instance: ServiceManager;
  private services: ServiceRegistry;
  private initialized = false;

  constructor() {
    this.services = {
      apiService,
      authService,
      rideService,
      driverService,
      tripService,
      webSocketService,
      locationService: LocationServiceInstance,
      syncService,
    };
  }

  public static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  /**
   * Initialize all services
   */
  async initialize(config?: ServiceConfig): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.log('Initializing services...');

      // Initialize sync service first (handles offline capabilities)
      if (config?.enableOfflineMode !== false) {
        await this.services.syncService.initialize();
      }

      // Initialize location service if enabled
      if (config?.enableLocationTracking !== false) {
        await this.services.locationService.requestPermissions();
      }

      this.initialized = true;
      console.log('Services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      throw error;
    }
  }

  /**
   * Get a specific service
   */
  getService<K extends keyof ServiceRegistry>(serviceName: K): ServiceRegistry[K] {
    const service = this.services[serviceName];
    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }
    return service;
  }

  /**
   * Get all services
   */
  getServices(): ServiceRegistry {
    return { ...this.services };
  }

  /**
   * Replace a service (useful for testing or customization)
   */
  replaceService<K extends keyof ServiceRegistry>(
    serviceName: K,
    newService: ServiceRegistry[K]
  ): void {
    this.services[serviceName] = newService;
  }

  /**
   * Check if services are initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Cleanup all services
   */
  async destroy(): Promise<void> {
    try {
      // Stop WebSocket connection
      this.services.webSocketService.disconnect();
      
      // Stop location updates
      this.services.locationService.stopLocationUpdates();
      
      // Destroy sync service
      this.services.syncService.destroy();
      
      this.initialized = false;
      console.log('Services destroyed');
    } catch (error) {
      console.error('Error destroying services:', error);
    }
  }
}

// Export service manager instance
export const serviceManager = ServiceManager.getInstance();

// Export individual services for direct access
export {
  apiService,
  authService,
  rideService,
  driverService,
  tripService,
  webSocketService,
  LocationServiceInstance as locationService,
  syncService,
};

// Export service classes for testing/customization
export {
  LocationService,
};

// Convenience hooks for React components
export const useServices = () => {
  return serviceManager.getServices();
};

export const useService = <K extends keyof ServiceRegistry>(serviceName: K) => {
  return serviceManager.getService(serviceName);
};

// Initialize services when module is loaded
// This ensures services are available immediately in the app
let initPromise: Promise<void> | null = null;

export const initializeServices = (config?: ServiceConfig): Promise<void> => {
  if (!initPromise) {
    initPromise = serviceManager.initialize(config);
  }
  return initPromise;
};

// Auto-initialize with default config
// This can be overridden by calling initializeServices explicitly
if (!serviceManager.isInitialized()) {
  initializeServices().catch(error => {
    console.error('Failed to auto-initialize services:', error);
  });
}

export default serviceManager;