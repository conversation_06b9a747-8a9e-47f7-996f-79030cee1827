export const colors = {
  // Primary Colors - Updated to golden yellow for buttons
  primary: '#FCCF2F',
  primaryDark: '#E6B829',
  
  // Backgrounds - Updated to dark theme
  background: '#2C2C2C',
  backgroundSecondary: '#3A3A3A',
  backgroundTertiary: '#404040',
  
  // Text Colors - Updated to white for dark theme
  text: '#ffffff',
  textSecondary: '#CCCCCC',
  textTertiary: '#999999',
  textLight: '#ffffff',
  
  // Semantic Colors - Adjusted for dark theme visibility
  success: '#4CAF50',
  successLight: '#1B5E20',
  warning: '#FF9800',
  warningLight: '#E65100',
  error: '#F44336',
  errorLight: '#B71C1C',
  info: '#2196F3',
  infoLight: '#0D47A1',
  
  // UI Elements - Updated for dark theme
  border: '#555555',
  inputBorder: '#666666',
  shadow: '#000000',
  
  // Interactive States - Updated for dark theme
  inactive: '#777777',
  disabled: '#555555',
  disabledText: '#777777',
  
  // Navigation & Cards - Updated for dark theme
  cardBackground: '#3A3A3A',
  activeBackground: '#404040',
  navigationActive: '#FCCF2F',
  navigationInactive: '#777777',
};

export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 4,
  },
  large: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.5,
    shadowRadius: 12,
    elevation: 8,
  },
};