const lightColors = {
  // Primary Colors - Golden yellow as requested
  primary: '#FCCF2F', // Golden yellow for buttons
  primaryDark: '#E6B829', // Darker golden yellow for pressed states
  
  // Backgrounds - Light mode
  background: '#FFFFFF', // Pure white main background
  backgroundSecondary: '#F8F9FA', // Light gray for cards and secondary areas
  backgroundTertiary: '#F1F3F5', // Slightly darker gray for elevated surfaces
  
  // Text Colors - Dark for light mode
  text: '#2C2C2C', // Dark gray for primary text (your specified dark color)
  textSecondary: '#495057', // Medium gray for secondary text
  textTertiary: '#6C757D', // Lighter gray for tertiary text
  textLight: '#FFFFFF', // Pure white for high contrast (on dark backgrounds)
  
  // Semantic Colors - Optimized for light background
  success: '#28A745', // Standard green that works on light background
  successLight: '#D4F8D4', // Light green background
  warning: '#FFC107', // Warm yellow that complements the golden theme
  warningLight: '#FFF3CD', // Light yellow background
  error: '#DC3545', // Standard red for light backgrounds
  errorLight: '#F8D7DA', // Light red background
  info: '#17A2B8', // Standard blue for light backgrounds
  infoLight: '#D6F5F8', // Light blue background
  
  // UI Elements - Light theme borders and elements
  border: '#DEE2E6', // Light gray for subtle borders
  inputBorder: '#CED4DA', // Medium gray for form elements
  shadow: '#000000', // Black for depth (with opacity)
  
  // Interactive States - Light theme grays
  inactive: '#6C757D', // Medium gray for inactive states
  disabled: '#E9ECEF', // Light gray for disabled elements
  disabledText: '#ADB5BD', // Muted gray text for disabled states
  
  // Navigation & Cards - Light elevated surfaces
  cardBackground: '#FFFFFF', // Pure white for cards
  activeBackground: '#F8F9FA', // Light gray for active states
  navigationActive: '#FCCF2F', // Golden yellow for active nav
  navigationInactive: '#6C757D', // Gray for inactive nav items
};

const darkColors = {
  // Primary Colors - Golden yellow as requested
  primary: '#FCCF2F', // Golden yellow for buttons
  primaryDark: '#E6B829', // Darker golden yellow for pressed states
  
  // Backgrounds - Dark gray as requested
  background: '#2C2C2C', // Main background dark gray
  backgroundSecondary: '#383838', // Slightly lighter for cards and secondary areas
  backgroundTertiary: '#454545', // Even lighter for elevated surfaces
  
  // Text Colors - White as requested
  text: '#FFFFFF', // Pure white for primary text
  textSecondary: '#E0E0E0', // Slightly muted white for secondary text
  textTertiary: '#B0B0B0', // More muted white for tertiary text
  textLight: '#FFFFFF', // Pure white for high contrast
  
  // Semantic Colors - Optimized for dark background
  success: '#4ADE80', // Bright green that pops on dark background
  successLight: '#22C55E', // Darker green for backgrounds
  warning: '#FBBF24', // Warm yellow-orange, complements the golden theme
  warningLight: '#F59E0B', // Darker amber
  error: '#F87171', // Soft red that's not too harsh
  errorLight: '#EF4444', // Darker red
  info: '#60A5FA', // Soft blue that works with the theme
  infoLight: '#3B82F6', // Darker blue
  
  // UI Elements - Subtle grays that work with the dark theme
  border: '#525252', // Medium gray for subtle borders
  inputBorder: '#6B7280', // Lighter gray for form elements
  shadow: '#000000', // Pure black for depth
  
  // Interactive States - Consistent grays
  inactive: '#737373', // Medium gray for inactive states
  disabled: '#404040', // Darker gray for disabled elements
  disabledText: '#8B8B8B', // Muted gray text for disabled states
  
  // Navigation & Cards - Elevated surfaces
  cardBackground: '#383838', // Slightly lighter than main background
  activeBackground: '#454545', // Active state background
  navigationActive: '#FCCF2F', // Golden yellow for active nav
  navigationInactive: '#737373', // Gray for inactive nav items
};

export const colors = lightColors; // Default to light mode

export const getColors = (isDarkMode: boolean) => {
  return isDarkMode ? darkColors : lightColors;
};

export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.5,
    shadowRadius: 6,
    elevation: 4,
  },
  large: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.6,
    shadowRadius: 12,
    elevation: 8,
  },
};