export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  vehicleType: string;
  licensePlate: string;
  currentLocation?: Location;
  acceptanceRate?: number;
  profilePhoto?: string;
  isOnline?: boolean;
}

// Status enums for better type safety and consistency
export type RideLifecycleStatus = 'pending' | 'accepted' | 'rejected' | 'ongoing' | 'completed' | 'cancelled';
export type BookingPriorityStatus = 'new' | 'urgent' | 'scheduled' | 'popular' | 'premium';
export type RidePriority = 'low' | 'medium' | 'high';
export type PaymentMethod = 'cash' | 'card' | 'wallet';
export type VehicleType = 'saloon' | 'estate' | 'executive' | 'people_carrier' | 'executive_people_carrier' | 'minibus_8';
export type TransferType = 'arrival' | 'departure';
export type FlightStatus = 'on_time' | 'delayed' | 'landed' | 'cancelled';

export interface Ride {
  id: string;
  passengerId: string;
  passengerName: string;
  passengerPhone: string;
  passengerPhoto?: string;
  pickupLocation: Location;
  dropoffLocation: Location;
  // Ride lifecycle status - used for filtering active/completed rides
  status: RideLifecycleStatus;
  // Booking priority/category - used for UI display and prioritization  
  bookingStatus: BookingPriorityStatus;
  fare: number;
  distance: number;
  duration: number;
  estimatedTime?: number;
  paymentMethod: PaymentMethod;
  specialRequests?: string;
  createdAt: Date;
  completedAt?: Date;
  rejectedAt?: Date;
  scheduledTime?: Date;
  preferredTime?: string;
  assignedDrivers?: string[];
  interestedDrivers?: string[];
  priority: RidePriority;
  // Airport transfer specific fields
  transferType: TransferType;
  flightNumber?: string;
  airline?: string;
  terminal?: string;
  flightTime?: Date;
  flightStatus?: FlightStatus;
  vehicleType: VehicleType;
  passengerCount: number;
  hasMeetAndGreet: boolean;
  airport: string;
}

export interface Location {
  latitude: number;
  longitude: number;
  address: string;
}

export interface NavigationOptions {
  app: 'google' | 'apple' | 'waze';
  deepLinkSupported: boolean;
  fallbackUrl: string;
}

export interface NavigationState {
  currentDestination?: Location;
  destinationLabel?: string;
  estimatedTime?: string;
  distance?: string;
  routePreview?: boolean;
}

export interface AirportLocation {
  code: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface Trip {
  id: string;
  rideId: string;
  earnings: number;
  distance: number;
  duration: number;
  completedAt: Date;
}

export interface AuthState {
  isAuthenticated: boolean;
  driver: Driver | null;
  token: string | null;
}

export interface AppState {
  currentRide: Ride | null;
  location: Location | null;
  trips: Trip[];
  totalEarnings: number;
  pendingBookings: Ride[];
  weeklyEarnings: number;
  todayTrips: number;
  availableDrivers: Driver[];
  sidebarOpen: boolean;
  navigationVisible: boolean;
  isOnline: boolean;
  queueStatus: 'available' | 'limited_queue' | 'service_mode' | 'completing';
  queuedBookings: Ride[];
  maxQueueSize: number;
  rideStatus: 'heading_to_pickup' | 'arrived_at_pickup' | 'passenger_onboard' | 'arrived_at_destination' | null;
  availableRides: Ride[];
  mockDataGenerated: boolean;
  mapRegion: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
}

export interface DriverStats {
  totalTrips: number;
  totalEarnings: number;
  acceptanceRate: number;
  cancellationRate: number;
  onlineHours: number;
}

// API State Management Types
export interface ApiLoadingState {
  auth: boolean;
  rides: boolean;
  trips: boolean;
  driver: boolean;
  earnings: boolean;
  bookings: boolean;
  navigation: boolean;
  communication: boolean;
}

export interface ApiErrorState {
  auth: string | null;
  rides: string | null;
  trips: string | null;
  driver: string | null;
  earnings: string | null;
  bookings: string | null;
  navigation: string | null;
  communication: string | null;
  network: string | null;
}

export interface ConnectionState {
  isOnline: boolean;
  isApiConnected: boolean;
  isWebSocketConnected: boolean;
  lastConnectionCheck: Date | null;
  retryCount: number;
}

export interface CacheState {
  rides: { data: any; timestamp: number } | null;
  trips: { data: any; timestamp: number } | null;
  earnings: { data: any; timestamp: number } | null;
  driver: { data: any; timestamp: number } | null;
}

export interface SyncState {
  pendingActions: PendingAction[];
  lastSyncTime: Date | null;
  isSyncing: boolean;
}

export interface PendingAction {
  id: string;
  type: string;
  payload: any;
  timestamp: Date;
  retryCount: number;
}