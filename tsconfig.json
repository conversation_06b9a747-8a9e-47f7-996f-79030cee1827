{"extends": "expo/tsconfig.base", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "bundler", "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "esnext", "strict": false, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}